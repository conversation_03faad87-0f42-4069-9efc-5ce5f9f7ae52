import { createServer } from "http";
import { storage } from "./storage";
import { setupAuth } from "./auth";
import { insertReplySchema, reviewFilterSchema } from "@shared/schema";
import { googleBusinessAPI, GoogleBusinessAPI } from "./services/google-business-api";
import { syncService } from "./services/sync-service";
import { initializeWebSocket } from "./services/websocket-service";
import { z } from "zod";
export async function registerRoutes(app) {
    // Set up authentication routes
    setupAuth(app);
    // User must be authenticated for all API routes except auth routes
    const requireAuth = (req, res, next) => {
        if (!req.isAuthenticated()) {
            return res.status(401).json({ message: "Unauthorized" });
        }
        next();
    };
    // Hotel routes
    app.get("/api/hotels", requireAuth, async (req, res) => {
        try {
            const hotels = await storage.listHotels();
            res.json(hotels);
        }
        catch (error) {
            res.status(500).json({ message: "Failed to fetch hotels" });
        }
    });
    // Review routes
    app.get("/api/reviews", requireAuth, async (req, res) => {
        try {
            const hotelId = Number(req.query.hotelId) || 1; // Default to hotel 1 for MVP
            // Parse and validate filter parameters
            const filterParams = {
                platform: req.query.platform,
                replyStatus: req.query.replyStatus,
                sortBy: req.query.sortBy,
                search: req.query.search,
                page: req.query.page ? Number(req.query.page) : 1,
                limit: req.query.limit ? Number(req.query.limit) : 10
            };
            const validatedFilters = reviewFilterSchema.parse(filterParams);
            // Convert reply status to boolean for filtering
            let isReplied = undefined;
            if (validatedFilters.replyStatus === 'replied') {
                isReplied = true;
            }
            else if (validatedFilters.replyStatus === 'not-replied') {
                isReplied = false;
            }
            const { reviews, total } = await storage.listReviews(hotelId, {
                platform: validatedFilters.platform !== 'all' ? validatedFilters.platform : undefined,
                isReplied,
                search: validatedFilters.search,
                sortBy: validatedFilters.sortBy,
                page: validatedFilters.page,
                limit: validatedFilters.limit
            });
            res.json({
                reviews,
                total,
                page: validatedFilters.page,
                limit: validatedFilters.limit,
                totalPages: Math.ceil(total / (validatedFilters.limit || 10))
            });
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                return res.status(400).json({ message: "Invalid filter parameters", errors: error.errors });
            }
            res.status(500).json({ message: "Failed to fetch reviews" });
        }
    });
    app.get("/api/reviews/:id", requireAuth, async (req, res) => {
        try {
            const id = Number(req.params.id);
            const review = await storage.getReview(id);
            if (!review) {
                return res.status(404).json({ message: "Review not found" });
            }
            res.json(review);
        }
        catch (error) {
            res.status(500).json({ message: "Failed to fetch review" });
        }
    });
    // Reply routes
    app.post("/api/reviews/:id/reply", requireAuth, async (req, res) => {
        try {
            const reviewId = Number(req.params.id);
            const review = await storage.getReview(reviewId);
            if (!review) {
                return res.status(404).json({ message: "Review not found" });
            }
            const replyData = insertReplySchema.parse({
                reviewId,
                content: req.body.content,
                userId: req.user.id
            });
            const reply = await storage.saveReply(replyData);
            // In a real app, this would make an API call to post the reply to the platform
            // For MVP, we mark it as posted locally
            res.json(reply);
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                return res.status(400).json({ message: "Invalid reply data", errors: error.errors });
            }
            res.status(500).json({ message: "Failed to save reply" });
        }
    });
    // Platform connection routes
    app.get("/api/platforms/connected", requireAuth, async (req, res) => {
        try {
            const hotelId = Number(req.query.hotelId) || 1; // Default to hotel 1 for MVP
            // Check which platforms are connected for this hotel
            const platforms = ["google", "booking", "airbnb", "tripadvisor"];
            const connectedPlatforms = await Promise.all(platforms.map(async (platform) => {
                const token = await storage.getPlatformToken(hotelId, platform);
                return {
                    platform,
                    connected: !!token
                };
            }));
            res.json(connectedPlatforms);
        }
        catch (error) {
            res.status(500).json({ message: "Failed to fetch connected platforms" });
        }
    });
    // Google OAuth routes
    app.get("/api/auth/google", async (req, res) => {
        try {
            const authUrl = googleBusinessAPI.getAuthUrl();
            res.json({ authUrl });
        }
        catch (error) {
            console.error("Error generating Google auth URL:", error);
            res.status(500).json({ message: "Failed to generate Google auth URL" });
        }
    });
    app.get("/api/auth/google/callback", requireAuth, async (req, res) => {
        try {
            const { code, state } = req.query;
            if (!code) {
                console.error("OAuth callback missing authorization code");
                return res.redirect("/platforms?error=missing_code");
            }
            // Exchange code for tokens
            const tokens = await googleBusinessAPI.getTokensFromCode(code);
            if (!tokens.access_token) {
                console.error("Failed to obtain access token from Google");
                return res.redirect("/platforms?error=token_exchange_failed");
            }
            // Store tokens in database
            const hotelId = Number(req.user?.hotelId) || 1;
            await storage.savePlatformToken({
                hotelId,
                platform: "google",
                accessToken: tokens.access_token,
                refreshToken: tokens.refresh_token || null,
                expiresAt: tokens.expiry_date || null,
            });
            console.log(`Successfully connected Google Business Profile for hotel ${hotelId}`);
            // Redirect to platforms page with success message
            res.redirect("/platforms?connected=google");
        }
        catch (error) {
            console.error("Error in Google OAuth callback:", error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            res.redirect(`/platforms?error=google_auth_failed&details=${encodeURIComponent(errorMessage)}`);
        }
    });
    // Google Business data routes
    app.get("/api/google/accounts", requireAuth, async (req, res) => {
        try {
            const hotelId = Number(req.user?.hotelId) || 1;
            const token = await storage.getPlatformToken(hotelId, "google");
            if (!token) {
                return res.status(401).json({ message: "Google account not connected" });
            }
            // Set credentials and get accounts
            googleBusinessAPI.setCredentials({
                access_token: token.accessToken,
                refresh_token: token.refreshToken || undefined,
                expiry_date: token.expiresAt || undefined,
            });
            const accounts = await googleBusinessAPI.getAccounts();
            res.json(accounts);
        }
        catch (error) {
            console.error("Error fetching Google accounts:", error);
            res.status(500).json({ message: "Failed to fetch Google Business accounts" });
        }
    });
    app.get("/api/google/locations", requireAuth, async (req, res) => {
        try {
            const hotelId = Number(req.user?.hotelId) || 1;
            const accountName = req.query.accountName;
            if (!accountName) {
                return res.status(400).json({ message: "Account name is required" });
            }
            const token = await storage.getPlatformToken(hotelId, "google");
            if (!token) {
                return res.status(401).json({ message: "Google account not connected" });
            }
            // Set credentials and get locations
            googleBusinessAPI.setCredentials({
                access_token: token.accessToken,
                refresh_token: token.refreshToken || undefined,
                expiry_date: token.expiresAt || undefined,
            });
            const locations = await googleBusinessAPI.getLocations(accountName);
            res.json(locations);
        }
        catch (error) {
            console.error("Error fetching Google locations:", error);
            res.status(500).json({ message: "Failed to fetch Google Business locations" });
        }
    });
    app.post("/api/google/sync-reviews", requireAuth, async (req, res) => {
        try {
            const hotelId = Number(req.user?.hotelId) || 1;
            const { locationName } = req.body;
            if (!locationName) {
                return res.status(400).json({ message: "Location name is required" });
            }
            const token = await storage.getPlatformToken(hotelId, "google");
            if (!token) {
                return res.status(401).json({ message: "Google account not connected" });
            }
            // Set credentials
            googleBusinessAPI.setCredentials({
                access_token: token.accessToken,
                refresh_token: token.refreshToken || undefined,
                expiry_date: token.expiresAt || undefined,
            });
            // Fetch reviews from Google
            const { reviews: googleReviews } = await googleBusinessAPI.getReviews(locationName);
            let syncedCount = 0;
            let skippedCount = 0;
            // Process each review
            for (const googleReview of googleReviews) {
                // Check if review already exists
                const existingReview = await storage.getReviewByExternalId("google", googleReview.reviewId);
                if (!existingReview) {
                    // Convert and save new review
                    const reviewData = GoogleBusinessAPI.convertToInternalReview(googleReview, hotelId);
                    await storage.saveReview(reviewData);
                    syncedCount++;
                }
                else {
                    skippedCount++;
                }
            }
            res.json({
                message: "Reviews synced successfully",
                syncedCount,
                skippedCount,
                totalProcessed: googleReviews.length,
            });
        }
        catch (error) {
            console.error("Error syncing Google reviews:", error);
            res.status(500).json({ message: "Failed to sync Google reviews" });
        }
    });
    // Sync management routes
    app.get("/api/sync/status", requireAuth, async (req, res) => {
        try {
            const hotelId = Number(req.user?.hotelId) || 1;
            const syncStatuses = await syncService.getSyncStatuses(hotelId);
            res.json(syncStatuses);
        }
        catch (error) {
            console.error("Error fetching sync status:", error);
            res.status(500).json({ message: "Failed to fetch sync status" });
        }
    });
    app.post("/api/sync/trigger", requireAuth, async (req, res) => {
        try {
            const { locationId, platform } = req.body;
            if (!locationId || !platform) {
                return res.status(400).json({ message: "Location ID and platform are required" });
            }
            const result = await syncService.triggerSync(Number(locationId), platform);
            res.json(result);
        }
        catch (error) {
            console.error("Error triggering sync:", error);
            res.status(500).json({ message: "Failed to trigger sync" });
        }
    });
    app.post("/api/sync/configure", requireAuth, async (req, res) => {
        try {
            const { locationId, platform, intervalMinutes, enabled } = req.body;
            if (!locationId || !platform) {
                return res.status(400).json({ message: "Location ID and platform are required" });
            }
            if (enabled) {
                await syncService.addLocationSync(Number(locationId), platform, intervalMinutes || 15);
            }
            else {
                await syncService.removeLocationSync(Number(locationId), platform);
            }
            res.json({ message: "Sync configuration updated successfully" });
        }
        catch (error) {
            console.error("Error configuring sync:", error);
            res.status(500).json({ message: "Failed to configure sync" });
        }
    });
    // Test endpoint to verify Google API setup (no auth required for testing)
    app.get("/api/google/test", async (req, res) => {
        try {
            // Test if Google API credentials are configured
            const authUrl = googleBusinessAPI.getAuthUrl();
            res.json({
                status: "Google API configured successfully",
                hasCredentials: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET),
                authUrl: authUrl.substring(0, 50) + "...", // Show partial URL for verification
                clientId: process.env.GOOGLE_CLIENT_ID ? process.env.GOOGLE_CLIENT_ID.substring(0, 20) + "..." : "Missing",
                redirectUri: process.env.GOOGLE_REDIRECT_URI
            });
        }
        catch (error) {
            res.status(500).json({
                status: "Google API configuration error",
                error: error instanceof Error ? error.message : 'Unknown error',
                hasCredentials: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET)
            });
        }
    });
    // Serve the test page
    app.get("/test-google", (req, res) => {
        res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Business API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .test-section { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>🔍 Google Business API Connection Test</h1>

    <div class="step">
        <h3>Step 1: Test Google API Configuration</h3>
        <button onclick="testGoogleConfig()">Test Google Config</button>
        <div id="google-config-result"></div>
    </div>

    <div class="step">
        <h3>Step 2: Test Authentication Flow</h3>
        <p><strong>Important:</strong> You need to be logged into your hotel dashboard first!</p>
        <button onclick="goToLogin()">Go to Login Page</button>
        <button onclick="goToPlatforms()">Go to Platforms Page</button>
        <div id="auth-result"></div>
    </div>

    <div class="step">
        <h3>Step 3: Manual Google Auth Test</h3>
        <button onclick="testGoogleAuth()">Get Google Auth URL</button>
        <div id="manual-auth-result"></div>
    </div>

    <div class="test-section">
        <h3>Debug Information</h3>
        <div id="debug-info">
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Server:</strong> ${req.get('host')}</p>
        </div>
    </div>

    <script>
        document.getElementById('current-url').textContent = window.location.href;

        async function testGoogleConfig() {
            const resultDiv = document.getElementById('google-config-result');
            resultDiv.innerHTML = '<p class="info">Testing Google API configuration...</p>';

            try {
                const response = await fetch('/api/google/test');
                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = \`
                        <p class="success">✅ Google API configured successfully</p>
                        <pre>\${JSON.stringify(data, null, 2)}</pre>
                    \`;
                } else {
                    resultDiv.innerHTML = \`
                        <p class="error">❌ Google API configuration error</p>
                        <pre>\${JSON.stringify(data, null, 2)}</pre>
                    \`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<p class="error">❌ Failed to test Google config: \${error.message}</p>\`;
            }
        }

        function goToLogin() {
            window.open('/auth', '_blank');
        }

        function goToPlatforms() {
            window.open('/platforms', '_blank');
        }

        async function testGoogleAuth() {
            const resultDiv = document.getElementById('manual-auth-result');
            resultDiv.innerHTML = '<p class="info">Getting Google Auth URL...</p>';

            try {
                const response = await fetch('/api/auth/google');
                const data = await response.json();

                if (response.ok && data.authUrl) {
                    resultDiv.innerHTML = \`
                        <p class="success">✅ Auth URL generated successfully</p>
                        <p><a href="\${data.authUrl}" target="_blank" style="color: #007bff; font-weight: bold;">🔗 Click here to authenticate with Google</a></p>
                        <p class="info">⚠️ Note: This will only work if you're logged into your hotel dashboard</p>
                        <details>
                            <summary>Full Auth URL</summary>
                            <pre>\${data.authUrl}</pre>
                        </details>
                    \`;
                } else {
                    resultDiv.innerHTML = \`
                        <p class="error">❌ Failed to generate auth URL</p>
                        <pre>\${JSON.stringify(data, null, 2)}</pre>
                        <p class="info">💡 This is expected if you're not logged in</p>
                    \`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<p class="error">❌ Auth flow failed: \${error.message}</p>\`;
            }
        }

        // Auto-test on page load
        window.onload = function() {
            testGoogleConfig();
        };
    </script>
</body>
</html>
    `);
    });
    // Statistics route
    app.get("/api/statistics", requireAuth, async (req, res) => {
        try {
            const hotelId = Number(req.query.hotelId) || 1; // Default to hotel 1 for MVP
            const { reviews, total } = await storage.listReviews(hotelId);
            // Calculate statistics
            const totalReviews = total;
            const repliedCount = reviews.filter(r => r.isReplied).length;
            const pendingReplies = totalReviews - repliedCount;
            // Calculate average rating
            let totalRating = 0;
            reviews.forEach(r => {
                totalRating += r.rating;
            });
            const averageRating = totalReviews > 0 ? (totalRating / totalReviews).toFixed(1) : "0";
            // Platform breakdown
            const platformCounts = {
                google: reviews.filter(r => r.platform === "google").length,
                booking: reviews.filter(r => r.platform === "booking").length,
                airbnb: reviews.filter(r => r.platform === "airbnb").length,
                tripadvisor: reviews.filter(r => r.platform === "tripadvisor").length
            };
            res.json({
                totalReviews,
                pendingReplies,
                averageRating,
                platformCounts
            });
        }
        catch (error) {
            res.status(500).json({ message: "Failed to fetch statistics" });
        }
    });
    const httpServer = createServer(app);
    // Initialize WebSocket service
    const webSocketService = initializeWebSocket(httpServer);
    console.log('WebSocket service initialized');
    // Start sync service
    syncService.start().catch(error => {
        console.error('Failed to start sync service:', error);
    });
    return httpServer;
}
//# sourceMappingURL=routes.js.map