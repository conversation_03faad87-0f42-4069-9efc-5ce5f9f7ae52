import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import request from 'supertest';
import { app } from '../index';
import { storage } from '../storage';
import { googleBusinessAPI } from '../services/google-business-api';

// Mock the storage and Google API
jest.mock('../storage');
jest.mock('../services/google-business-api');

const mockStorage = storage as jest.Mocked<typeof storage>;
const mockGoogleAPI = googleBusinessAPI as jest.Mocked<typeof googleBusinessAPI>;

describe('Google Business Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock authentication middleware
    jest.spyOn(require('../middleware/auth'), 'requireAuth').mockImplementation((req: any, res: any, next: any) => {
      req.user = { id: 1, hotelId: 1, email: '<EMAIL>' };
      next();
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('OAuth Flow', () => {
    it('should initiate Google OAuth flow', async () => {
      mockGoogleAPI.getAuthUrl.mockReturnValue('https://accounts.google.com/oauth2/auth?...');

      const response = await request(app)
        .get('/api/auth/google')
        .expect(302);

      expect(response.headers.location).toContain('accounts.google.com');
      expect(mockGoogleAPI.getAuthUrl).toHaveBeenCalled();
    });

    it('should handle OAuth callback successfully', async () => {
      const mockTokens = {
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        expiry_date: Date.now() + 3600000
      };

      mockGoogleAPI.getTokensFromCode.mockResolvedValue(mockTokens);
      mockStorage.savePlatformToken.mockResolvedValue(undefined);

      const response = await request(app)
        .get('/api/auth/google/callback?code=test-code')
        .expect(302);

      expect(response.headers.location).toBe('/platforms?connected=google');
      expect(mockGoogleAPI.getTokensFromCode).toHaveBeenCalledWith('test-code');
      expect(mockStorage.savePlatformToken).toHaveBeenCalledWith({
        hotelId: 1,
        platform: 'google',
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
        expiresAt: expect.any(Number)
      });
    });

    it('should handle OAuth callback errors', async () => {
      mockGoogleAPI.getTokensFromCode.mockRejectedValue(new Error('Invalid code'));

      const response = await request(app)
        .get('/api/auth/google/callback?code=invalid-code')
        .expect(302);

      expect(response.headers.location).toBe('/platforms?error=google_auth_failed');
    });
  });

  describe('Account and Location Management', () => {
    beforeEach(() => {
      mockStorage.getPlatformToken.mockResolvedValue({
        id: 1,
        hotelId: 1,
        platform: 'google',
        accessToken: 'test-token',
        refreshToken: 'test-refresh',
        expiresAt: Date.now() + 3600000,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });
    });

    it('should fetch Google Business accounts', async () => {
      const mockAccounts = [
        { name: 'accounts/123', displayName: 'Test Hotel Account' }
      ];

      mockGoogleAPI.getAccounts.mockResolvedValue(mockAccounts);

      const response = await request(app)
        .get('/api/google/accounts')
        .expect(200);

      expect(response.body.accounts).toEqual(mockAccounts);
      expect(mockGoogleAPI.setCredentials).toHaveBeenCalledWith({
        access_token: 'test-token',
        refresh_token: 'test-refresh',
        expiry_date: expect.any(Number)
      });
    });

    it('should fetch business locations', async () => {
      const mockLocations = [
        { 
          name: 'accounts/123/locations/456',
          title: 'Test Hotel',
          storefrontAddress: {
            addressLines: ['123 Main St'],
            locality: 'Test City'
          }
        }
      ];

      mockGoogleAPI.getLocations.mockResolvedValue(mockLocations);

      const response = await request(app)
        .get('/api/google/locations?accountName=accounts/123')
        .expect(200);

      expect(response.body.locations).toEqual(mockLocations);
      expect(mockGoogleAPI.getLocations).toHaveBeenCalledWith('accounts/123');
    });

    it('should handle missing account parameter', async () => {
      const response = await request(app)
        .get('/api/google/locations')
        .expect(400);

      expect(response.body.message).toBe('Account name is required');
    });
  });

  describe('Review Management', () => {
    beforeEach(() => {
      mockStorage.getPlatformToken.mockResolvedValue({
        id: 1,
        hotelId: 1,
        platform: 'google',
        accessToken: 'test-token',
        refreshToken: 'test-refresh',
        expiresAt: Date.now() + 3600000,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });
    });

    it('should sync reviews successfully', async () => {
      const mockReviews = [
        {
          name: 'accounts/123/locations/456/reviews/789',
          reviewId: '789',
          reviewer: { displayName: 'John Doe' },
          starRating: 'FIVE',
          comment: 'Great hotel!',
          createTime: '2023-01-01T00:00:00Z',
          updateTime: '2023-01-01T00:00:00Z'
        }
      ];

      mockGoogleAPI.getReviews.mockResolvedValue({
        reviews: mockReviews,
        totalReviewCount: 1
      });

      mockStorage.saveReviews.mockResolvedValue([{
        id: 1,
        hotelId: 1,
        platform: 'google',
        externalId: '789',
        authorName: 'John Doe',
        authorImage: null,
        rating: 5,
        content: 'Great hotel!',
        date: new Date('2023-01-01T00:00:00Z').getTime(),
        createdAt: Date.now(),
        updatedAt: Date.now()
      }]);

      const response = await request(app)
        .post('/api/google/sync-reviews')
        .send({ locationName: 'accounts/123/locations/456' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.reviewsCount).toBe(1);
      expect(mockGoogleAPI.getReviews).toHaveBeenCalledWith('accounts/123/locations/456');
    });

    it('should reply to reviews', async () => {
      mockGoogleAPI.replyToReview.mockResolvedValue({
        success: true,
        message: 'Review reply posted successfully'
      });

      mockStorage.saveReply.mockResolvedValue({
        id: 1,
        reviewId: 123,
        content: 'Thank you for your feedback!',
        userId: 1,
        platform: 'google',
        externalReplyId: 'google-reply-123',
        status: 'posted',
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      const response = await request(app)
        .post('/api/google/reply/123')
        .send({ replyText: 'Thank you for your feedback!' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Review reply posted successfully');
      expect(mockGoogleAPI.replyToReview).toHaveBeenCalledWith('123', 'Thank you for your feedback!');
    });

    it('should handle empty reply text', async () => {
      const response = await request(app)
        .post('/api/google/reply/123')
        .send({ replyText: '' })
        .expect(400);

      expect(response.body.message).toBe('Reply text is required');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing Google token', async () => {
      mockStorage.getPlatformToken.mockResolvedValue(null);

      const response = await request(app)
        .get('/api/google/accounts')
        .expect(401);

      expect(response.body.message).toBe('Google account not connected');
    });

    it('should handle API errors gracefully', async () => {
      mockStorage.getPlatformToken.mockResolvedValue({
        id: 1,
        hotelId: 1,
        platform: 'google',
        accessToken: 'test-token',
        refreshToken: 'test-refresh',
        expiresAt: Date.now() + 3600000,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      mockGoogleAPI.getAccounts.mockRejectedValue(new Error('API Error'));

      const response = await request(app)
        .get('/api/google/accounts')
        .expect(500);

      expect(response.body.message).toBe('Failed to fetch Google Business accounts');
    });
  });

  describe('Configuration Test', () => {
    it('should test Google API configuration', async () => {
      const response = await request(app)
        .get('/api/google/test')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toContain('Google API configuration test completed');
    });
  });
});
