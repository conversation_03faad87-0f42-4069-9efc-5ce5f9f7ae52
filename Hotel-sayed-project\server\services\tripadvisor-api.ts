import axios, { AxiosInstance } from 'axios';
import { storage } from '../storage';

export interface TripAdvisorLocation {
  location_id: string;
  name: string;
  address_obj: {
    street1?: string;
    street2?: string;
    city?: string;
    state?: string;
    country?: string;
    postalcode?: string;
    address_string?: string;
  };
  phone?: string;
  website?: string;
  email?: string;
  latitude?: string;
  longitude?: string;
  num_reviews?: string;
  rating?: string;
  ranking?: string;
}

export interface TripAdvisorReview {
  id: string;
  lang: string;
  location_id: string;
  published_date: string;
  rating: number;
  helpful_votes: number;
  rating_image_url: string;
  url: string;
  trip_type: string;
  travel_date: string;
  text: string;
  title: string;
  user: {
    user_id: string;
    member_id: string;
    type: string;
    first_name: string;
    last_name?: string;
    avatar: {
      thumbnail: string;
      small: string;
      medium: string;
      large: string;
      original: string;
    };
  };
  is_machine_translated: boolean;
  subratings?: {
    [key: string]: {
      name: string;
      rating_image_url: string;
      value: string;
      localized_name: string;
    };
  };
  management_response?: {
    id: string;
    lang: string;
    published_date: string;
    text: string;
    user: {
      user_id: string;
      member_id: string;
      type: string;
      first_name: string;
      last_name?: string;
    };
  };
}

export interface TripAdvisorAPIResponse<T> {
  data: T[];
  paging?: {
    results: string;
    total_results: string;
    offset: string;
    limit: string;
  };
  error?: {
    message: string;
    code: string;
  };
}

export class TripAdvisorAPI {
  private apiClient: AxiosInstance;
  private apiKey: string;
  private baseURL = 'https://api.content.tripadvisor.com/api/v1';

  constructor() {
    this.apiKey = process.env.TRIPADVISOR_API_KEY || '';
    
    if (!this.apiKey) {
      console.warn('TripAdvisor API key not configured. Set TRIPADVISOR_API_KEY environment variable.');
    }

    this.apiClient = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor to include API key
    this.apiClient.interceptors.request.use((config) => {
      if (this.apiKey) {
        config.params = {
          ...config.params,
          key: this.apiKey,
        };
      }
      return config;
    });

    // Add response interceptor for error handling
    this.apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('TripAdvisor API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Search for locations by name and address
   */
  async searchLocations(searchQuery: string, category?: string): Promise<TripAdvisorLocation[]> {
    try {
      if (!this.apiKey) {
        console.warn('TripAdvisor API key not configured, returning mock data');
        return this.getMockLocations(searchQuery);
      }

      const response = await this.apiClient.get('/location/search', {
        params: {
          searchQuery,
          category: category || 'hotels',
          language: 'en',
        },
      });

      return response.data.data || [];
    } catch (error) {
      console.error('Error searching TripAdvisor locations:', error);
      // Return mock data as fallback
      return this.getMockLocations(searchQuery);
    }
  }

  /**
   * Get location details by location ID
   */
  async getLocationDetails(locationId: string): Promise<TripAdvisorLocation | null> {
    try {
      if (!this.apiKey) {
        console.warn('TripAdvisor API key not configured, returning mock data');
        return this.getMockLocationDetails(locationId);
      }

      const response = await this.apiClient.get(`/location/${locationId}/details`, {
        params: {
          language: 'en',
          currency: 'USD',
        },
      });

      return response.data || null;
    } catch (error) {
      console.error('Error fetching TripAdvisor location details:', error);
      return this.getMockLocationDetails(locationId);
    }
  }

  /**
   * Get reviews for a specific location
   */
  async getReviews(
    locationId: string,
    limit: number = 20,
    offset: number = 0,
    language: string = 'en'
  ): Promise<{
    reviews: TripAdvisorReview[];
    totalResults: number;
    hasMore: boolean;
  }> {
    try {
      if (!this.apiKey) {
        console.warn('TripAdvisor API key not configured, returning mock data');
        return this.getMockReviews(locationId, limit, offset);
      }

      const response = await this.apiClient.get(`/location/${locationId}/reviews`, {
        params: {
          language,
          limit,
          offset,
        },
      });

      const data = response.data;
      const reviews = data.data || [];
      const totalResults = parseInt(data.paging?.total_results || '0');
      const hasMore = (offset + limit) < totalResults;

      return {
        reviews,
        totalResults,
        hasMore,
      };
    } catch (error) {
      console.error('Error fetching TripAdvisor reviews:', error);
      return this.getMockReviews(locationId, limit, offset);
    }
  }

  /**
   * Convert TripAdvisor review to internal format
   */
  static convertToInternalReview(tripAdvisorReview: TripAdvisorReview, hotelId: number) {
    return {
      hotelId,
      platform: 'tripadvisor',
      externalId: tripAdvisorReview.id,
      authorName: `${tripAdvisorReview.user.first_name} ${tripAdvisorReview.user.last_name || ''}`.trim(),
      authorImage: tripAdvisorReview.user.avatar?.medium || tripAdvisorReview.user.avatar?.small,
      rating: tripAdvisorReview.rating,
      content: `${tripAdvisorReview.title}\n\n${tripAdvisorReview.text}`.trim(),
      date: new Date(tripAdvisorReview.published_date).getTime(),
      // TripAdvisor specific fields
      tripAdvisorLocationId: tripAdvisorReview.location_id,
      tripAdvisorUrl: tripAdvisorReview.url,
      tripType: tripAdvisorReview.trip_type,
      travelDate: tripAdvisorReview.travel_date,
      helpfulVotes: tripAdvisorReview.helpful_votes,
      hasManagementResponse: !!tripAdvisorReview.management_response,
      lastSyncAt: Date.now(),
    };
  }

  /**
   * Mock data for development/testing when API key is not available
   */
  private getMockLocations(searchQuery: string): TripAdvisorLocation[] {
    return [
      {
        location_id: 'mock-location-1',
        name: `${searchQuery} - Mock Hotel`,
        address_obj: {
          street1: '123 Mock Street',
          city: 'Mock City',
          state: 'Mock State',
          country: 'Mock Country',
          postalcode: '12345',
          address_string: '123 Mock Street, Mock City, Mock State 12345, Mock Country',
        },
        phone: '******-0123',
        website: 'https://mockhotel.com',
        latitude: '40.7128',
        longitude: '-74.0060',
        num_reviews: '150',
        rating: '4.5',
        ranking: '#1 of 100 hotels in Mock City',
      },
    ];
  }

  private getMockLocationDetails(locationId: string): TripAdvisorLocation {
    return {
      location_id: locationId,
      name: 'Mock Hotel Details',
      address_obj: {
        street1: '123 Mock Street',
        city: 'Mock City',
        state: 'Mock State',
        country: 'Mock Country',
        postalcode: '12345',
        address_string: '123 Mock Street, Mock City, Mock State 12345, Mock Country',
      },
      phone: '******-0123',
      website: 'https://mockhotel.com',
      email: '<EMAIL>',
      latitude: '40.7128',
      longitude: '-74.0060',
      num_reviews: '150',
      rating: '4.5',
      ranking: '#1 of 100 hotels in Mock City',
    };
  }

  private getMockReviews(locationId: string, limit: number, offset: number): {
    reviews: TripAdvisorReview[];
    totalResults: number;
    hasMore: boolean;
  } {
    const mockReviews: TripAdvisorReview[] = [
      {
        id: `mock-review-${Date.now()}-1`,
        lang: 'en',
        location_id: locationId,
        published_date: new Date(Date.now() - 86400000).toISOString(),
        rating: 5,
        helpful_votes: 3,
        rating_image_url: 'https://static.tacdn.com/img2/ratings/traveler/5.0.svg',
        url: `https://tripadvisor.com/mock-review-1`,
        trip_type: 'Business',
        travel_date: new Date(Date.now() - *********).toISOString(),
        text: 'Excellent service and beautiful location. The staff was very helpful and the rooms were clean and comfortable.',
        title: 'Outstanding Experience',
        user: {
          user_id: 'mock-user-1',
          member_id: 'mock-member-1',
          type: 'user',
          first_name: 'John',
          last_name: 'D',
          avatar: {
            thumbnail: 'https://media-cdn.tripadvisor.com/media/photo-t/01/2e/70/d8/avatar.jpg',
            small: 'https://media-cdn.tripadvisor.com/media/photo-s/01/2e/70/d8/avatar.jpg',
            medium: 'https://media-cdn.tripadvisor.com/media/photo-m/01/2e/70/d8/avatar.jpg',
            large: 'https://media-cdn.tripadvisor.com/media/photo-l/01/2e/70/d8/avatar.jpg',
            original: 'https://media-cdn.tripadvisor.com/media/photo-o/01/2e/70/d8/avatar.jpg',
          },
        },
        is_machine_translated: false,
      },
    ];

    return {
      reviews: mockReviews,
      totalResults: 50,
      hasMore: (offset + limit) < 50,
    };
  }
}

// Export singleton instance
export const tripAdvisorAPI = new TripAdvisorAPI();
