import React, { useEffect, useState } from 'react';
import { useWebSocket, NewReviewsNotification, SyncUpdate } from '../hooks/use-websocket';
import { CheckCircle, AlertCircle, X, Bell } from 'lucide-react';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'info';
  title: string;
  message: string;
  timestamp: number;
  autoClose?: boolean;
}

export function NotificationSystem() {
  const { onSyncUpdate, onNewReviews, onSyncError } = useWebSocket();
  const [notifications, setNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    // Listen for new reviews
    const cleanupNewReviews = onNewReviews((notification: NewReviewsNotification) => {
      addNotification({
        type: 'success',
        title: 'New Reviews!',
        message: notification.message,
        autoClose: true,
      });
    });

    // Listen for sync updates
    const cleanupSyncUpdate = onSyncUpdate((update: SyncUpdate) => {
      if (update.status === 'completed' && update.newReviews > 0) {
        addNotification({
          type: 'success',
          title: 'Sync Completed',
          message: `Successfully synced ${update.newReviews} new reviews from ${update.platform}`,
          autoClose: true,
        });
      }
    });

    // Listen for sync errors
    const cleanupSyncError = onSyncError((error: any) => {
      addNotification({
        type: 'error',
        title: 'Sync Failed',
        message: error.error || 'An error occurred during sync',
        autoClose: false,
      });
    });

    return () => {
      if (cleanupNewReviews) cleanupNewReviews();
      if (cleanupSyncUpdate) cleanupSyncUpdate();
      if (cleanupSyncError) cleanupSyncError();
    };
  }, [onNewReviews, onSyncUpdate, onSyncError]);

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: Date.now(),
    };

    setNotifications(prev => [newNotification, ...prev]);

    // Auto-close notification after 5 seconds if autoClose is true
    if (notification.autoClose) {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, 5000);
    }
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'info':
        return <Bell className="w-5 h-5 text-blue-500" />;
      default:
        return <Bell className="w-5 h-5 text-gray-500" />;
    }
  };

  const getBackgroundColor = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'info':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`p-4 rounded-lg border shadow-lg transition-all duration-300 ${getBackgroundColor(notification.type)}`}
        >
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              {getIcon(notification.type)}
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-gray-900">
                {notification.title}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {notification.message}
              </p>
              <p className="text-xs text-gray-400 mt-2">
                {new Date(notification.timestamp).toLocaleTimeString()}
              </p>
            </div>
            <button
              onClick={() => removeNotification(notification.id)}
              className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}
