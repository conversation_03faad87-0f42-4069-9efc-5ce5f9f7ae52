import { users, hotels, platformTokens, reviews, replies, googleBusinessLocations, tripAdvisorLocations, bookingProperties, airbnbListings, syncStatus } from "@shared/schema";
import session from "express-session";
import { eq, and, asc, desc, sql, count } from "drizzle-orm";
import { db } from "./db";
import createMemoryStore from "memorystore";
const MemoryStore = createMemoryStore(session);
// In-memory storage implementation
export class DatabaseStorage {
    sessionStore;
    constructor() {
        this.sessionStore = new MemoryStore({
            checkPeriod: 86400000 // prune expired entries every 24h
        });
        // Create a default hotel for demonstration
        this.checkAndCreateDefaultHotel();
    }
    async checkAndCreateDefaultHotel() {
        const existingHotels = await db.select().from(hotels);
        if (existingHotels.length === 0) {
            await this.createHotel({
                name: "Hotel Majestic",
                address: "123 Main Street, Anytown, USA"
            });
        }
    }
    // User operations
    async getUser(id) {
        const results = await db.select().from(users).where(eq(users.id, id));
        return results.length > 0 ? results[0] : undefined;
    }
    async getUserByUsername(username) {
        const results = await db.select().from(users).where(eq(users.username, username));
        return results.length > 0 ? results[0] : undefined;
    }
    async createUser(insertUser) {
        const results = await db.insert(users)
            .values({ ...insertUser, role: "staff", hotelId: 1 })
            .returning();
        return results[0];
    }
    // Hotel operations
    async getHotel(id) {
        const results = await db.select().from(hotels).where(eq(hotels.id, id));
        return results.length > 0 ? results[0] : undefined;
    }
    async createHotel(insertHotel) {
        const results = await db.insert(hotels)
            .values(insertHotel)
            .returning();
        return results[0];
    }
    async listHotels() {
        return db.select().from(hotels);
    }
    // Platform token operations
    async getPlatformToken(hotelId, platform) {
        const results = await db.select().from(platformTokens)
            .where(and(eq(platformTokens.hotelId, hotelId), eq(platformTokens.platform, platform)));
        return results.length > 0 ? results[0] : undefined;
    }
    async savePlatformToken(insertToken) {
        // Check if token already exists for this hotel/platform combination
        const existingToken = await this.getPlatformToken(insertToken.hotelId, insertToken.platform);
        if (existingToken) {
            // Update existing token
            const results = await db.update(platformTokens)
                .set({
                accessToken: insertToken.accessToken,
                refreshToken: insertToken.refreshToken,
                expiresAt: insertToken.expiresAt,
                googleAccountId: insertToken.googleAccountId,
                googleAccountName: insertToken.googleAccountName
            })
                .where(eq(platformTokens.id, existingToken.id))
                .returning();
            return results[0];
        }
        else {
            // Create new token
            const results = await db.insert(platformTokens)
                .values(insertToken)
                .returning();
            return results[0];
        }
    }
    // Review operations
    async getReview(id) {
        const results = await db.select().from(reviews).where(eq(reviews.id, id));
        if (results.length === 0)
            return undefined;
        const review = results[0];
        // Check if there's a reply for this review
        const reply = await this.getReplyByReviewId(id);
        if (reply) {
            return { ...review, reply };
        }
        return review;
    }
    async getReviewByExternalId(platform, externalId) {
        const results = await db.select().from(reviews)
            .where(and(eq(reviews.platform, platform), eq(reviews.externalId, externalId)));
        if (results.length === 0)
            return undefined;
        const review = results[0];
        // Check if there's a reply for this review
        const reply = await this.getReplyByReviewId(review.id);
        if (reply) {
            return { ...review, reply };
        }
        return review;
    }
    async listReviews(hotelId, filters) {
        // Build the where conditions
        let whereConditions = [eq(reviews.hotelId, hotelId)];
        if (filters) {
            if (filters.platform && filters.platform !== 'all') {
                whereConditions.push(eq(reviews.platform, filters.platform));
            }
            if (filters.isReplied !== undefined) {
                whereConditions.push(eq(reviews.isReplied, filters.isReplied ? 1 : 0));
            }
            if (filters.search) {
                const searchPattern = `%${filters.search}%`;
                whereConditions.push(sql `(${reviews.content} ILIKE ${searchPattern} OR ${reviews.authorName} ILIKE ${searchPattern})`);
            }
        }
        // Count total matching reviews
        const countResult = await db.select({ count: count() })
            .from(reviews)
            .where(and(...whereConditions));
        const total = Number(countResult[0].count);
        // Build the query for reviews
        let reviewsList = [];
        // Determine sort order based on filters
        const sortBy = filters?.sortBy || 'date-desc';
        // Apply correct Drizzle query based on sort and pagination
        try {
            // Use a simpler approach just using the Drizzle query builder with separate cases
            // to work around TypeScript limitations
            if (filters?.page !== undefined && filters?.limit !== undefined) {
                // With pagination
                const offset = (filters.page - 1) * filters.limit;
                if (sortBy === 'date-desc') {
                    reviewsList = await db.select().from(reviews)
                        .where(and(...whereConditions))
                        .orderBy(desc(reviews.date))
                        .limit(filters.limit)
                        .offset(offset);
                }
                else if (sortBy === 'date-asc') {
                    reviewsList = await db.select().from(reviews)
                        .where(and(...whereConditions))
                        .orderBy(asc(reviews.date))
                        .limit(filters.limit)
                        .offset(offset);
                }
                else if (sortBy === 'rating-desc') {
                    reviewsList = await db.select().from(reviews)
                        .where(and(...whereConditions))
                        .orderBy(desc(reviews.rating))
                        .limit(filters.limit)
                        .offset(offset);
                }
                else if (sortBy === 'rating-asc') {
                    reviewsList = await db.select().from(reviews)
                        .where(and(...whereConditions))
                        .orderBy(asc(reviews.rating))
                        .limit(filters.limit)
                        .offset(offset);
                }
                else {
                    // Default sort
                    reviewsList = await db.select().from(reviews)
                        .where(and(...whereConditions))
                        .orderBy(desc(reviews.date))
                        .limit(filters.limit)
                        .offset(offset);
                }
            }
            else {
                // Without pagination
                if (sortBy === 'date-desc') {
                    reviewsList = await db.select().from(reviews)
                        .where(and(...whereConditions))
                        .orderBy(desc(reviews.date));
                }
                else if (sortBy === 'date-asc') {
                    reviewsList = await db.select().from(reviews)
                        .where(and(...whereConditions))
                        .orderBy(asc(reviews.date));
                }
                else if (sortBy === 'rating-desc') {
                    reviewsList = await db.select().from(reviews)
                        .where(and(...whereConditions))
                        .orderBy(desc(reviews.rating));
                }
                else if (sortBy === 'rating-asc') {
                    reviewsList = await db.select().from(reviews)
                        .where(and(...whereConditions))
                        .orderBy(asc(reviews.rating));
                }
                else {
                    // Default sort
                    reviewsList = await db.select().from(reviews)
                        .where(and(...whereConditions))
                        .orderBy(desc(reviews.date));
                }
            }
        }
        catch (error) {
            console.error('Error fetching reviews:', error);
            // Return empty list on error
            reviewsList = [];
        }
        // Attach replies to reviews
        const reviewsWithReplies = await Promise.all(reviewsList.map(async (review) => {
            const reply = await this.getReplyByReviewId(review.id);
            if (reply) {
                return { ...review, reply };
            }
            return review;
        }));
        return { reviews: reviewsWithReplies, total };
    }
    async saveReview(insertReview) {
        const results = await db.insert(reviews)
            .values({ ...insertReview, isReplied: 0 })
            .returning();
        return results[0];
    }
    // Reply operations
    async getReply(id) {
        const results = await db.select().from(replies).where(eq(replies.id, id));
        return results.length > 0 ? results[0] : undefined;
    }
    async getReplyByReviewId(reviewId) {
        const results = await db.select().from(replies).where(eq(replies.reviewId, reviewId));
        return results.length > 0 ? results[0] : undefined;
    }
    async saveReply(insertReply) {
        const results = await db.insert(replies)
            .values({
            ...insertReply,
            date: Date.now(),
            isPosted: 0
        })
            .returning();
        // Mark the review as replied
        await this.markReviewAsReplied(insertReply.reviewId);
        return results[0];
    }
    async markReviewAsReplied(reviewId) {
        await db.update(reviews)
            .set({ isReplied: 1 })
            .where(eq(reviews.id, reviewId));
    }
    // Google Business Location operations
    async getGoogleBusinessLocation(id) {
        const results = await db.select().from(googleBusinessLocations).where(eq(googleBusinessLocations.id, id));
        return results.length > 0 ? results[0] : undefined;
    }
    async getGoogleBusinessLocationByGoogleId(googleLocationId) {
        const results = await db.select().from(googleBusinessLocations)
            .where(eq(googleBusinessLocations.googleLocationId, googleLocationId));
        return results.length > 0 ? results[0] : undefined;
    }
    async listGoogleBusinessLocations(hotelId) {
        return db.select().from(googleBusinessLocations)
            .where(eq(googleBusinessLocations.hotelId, hotelId));
    }
    async saveGoogleBusinessLocation(insertLocation) {
        // Check if location already exists
        const existing = await this.getGoogleBusinessLocationByGoogleId(insertLocation.googleLocationId);
        if (existing) {
            // Update existing location
            const results = await db.update(googleBusinessLocations)
                .set({
                ...insertLocation,
                updatedAt: Date.now()
            })
                .where(eq(googleBusinessLocations.id, existing.id))
                .returning();
            return results[0];
        }
        else {
            // Create new location
            const results = await db.insert(googleBusinessLocations)
                .values({
                ...insertLocation,
                createdAt: Date.now(),
                updatedAt: Date.now(),
            })
                .returning();
            return results[0];
        }
    }
    async updateGoogleBusinessLocation(id, updates) {
        const results = await db.update(googleBusinessLocations)
            .set({
            ...updates,
            updatedAt: Date.now()
        })
            .where(eq(googleBusinessLocations.id, id))
            .returning();
        return results[0];
    }
    // TripAdvisor Location operations
    async getTripAdvisorLocation(id) {
        const results = await db.select().from(tripAdvisorLocations).where(eq(tripAdvisorLocations.id, id));
        return results.length > 0 ? results[0] : undefined;
    }
    async getTripAdvisorLocationByLocationId(tripAdvisorLocationId) {
        const results = await db.select().from(tripAdvisorLocations)
            .where(eq(tripAdvisorLocations.tripAdvisorLocationId, tripAdvisorLocationId));
        return results.length > 0 ? results[0] : undefined;
    }
    async listTripAdvisorLocations(hotelId) {
        return db.select().from(tripAdvisorLocations)
            .where(eq(tripAdvisorLocations.hotelId, hotelId));
    }
    async saveTripAdvisorLocation(insertLocation) {
        // Check if location already exists
        const existing = await this.getTripAdvisorLocationByLocationId(insertLocation.tripAdvisorLocationId);
        if (existing) {
            // Update existing location
            const results = await db.update(tripAdvisorLocations)
                .set({
                ...insertLocation,
                updatedAt: Date.now()
            })
                .where(eq(tripAdvisorLocations.id, existing.id))
                .returning();
            return results[0];
        }
        else {
            // Create new location
            const results = await db.insert(tripAdvisorLocations)
                .values({
                ...insertLocation,
                createdAt: Date.now(),
                updatedAt: Date.now(),
            })
                .returning();
            return results[0];
        }
    }
    async updateTripAdvisorLocation(id, updates) {
        const results = await db.update(tripAdvisorLocations)
            .set({
            ...updates,
            updatedAt: Date.now()
        })
            .where(eq(tripAdvisorLocations.id, id))
            .returning();
        return results[0];
    }
    // Booking.com Property operations
    async getBookingProperty(id) {
        const results = await db.select().from(bookingProperties).where(eq(bookingProperties.id, id));
        return results.length > 0 ? results[0] : undefined;
    }
    async getBookingPropertyByHotelId(bookingHotelId) {
        const results = await db.select().from(bookingProperties)
            .where(eq(bookingProperties.bookingHotelId, bookingHotelId));
        return results.length > 0 ? results[0] : undefined;
    }
    async listBookingProperties(hotelId) {
        return db.select().from(bookingProperties)
            .where(eq(bookingProperties.hotelId, hotelId));
    }
    async saveBookingProperty(insertProperty) {
        // Check if property already exists
        const existing = await this.getBookingPropertyByHotelId(insertProperty.bookingHotelId);
        if (existing) {
            // Update existing property
            const results = await db.update(bookingProperties)
                .set({
                ...insertProperty,
                updatedAt: Date.now()
            })
                .where(eq(bookingProperties.id, existing.id))
                .returning();
            return results[0];
        }
        else {
            // Create new property
            const results = await db.insert(bookingProperties)
                .values({
                ...insertProperty,
                createdAt: Date.now(),
                updatedAt: Date.now(),
            })
                .returning();
            return results[0];
        }
    }
    async updateBookingProperty(id, updates) {
        const results = await db.update(bookingProperties)
            .set({
            ...updates,
            updatedAt: Date.now()
        })
            .where(eq(bookingProperties.id, id))
            .returning();
        return results[0];
    }
    // Airbnb Listing operations
    async getAirbnbListing(id) {
        const results = await db.select().from(airbnbListings).where(eq(airbnbListings.id, id));
        return results.length > 0 ? results[0] : undefined;
    }
    async getAirbnbListingByListingId(airbnbListingId) {
        const results = await db.select().from(airbnbListings)
            .where(eq(airbnbListings.airbnbListingId, airbnbListingId));
        return results.length > 0 ? results[0] : undefined;
    }
    async listAirbnbListings(hotelId) {
        return db.select().from(airbnbListings)
            .where(eq(airbnbListings.hotelId, hotelId));
    }
    async saveAirbnbListing(insertListing) {
        // Check if listing already exists
        const existing = await this.getAirbnbListingByListingId(insertListing.airbnbListingId);
        if (existing) {
            // Update existing listing
            const results = await db.update(airbnbListings)
                .set({
                ...insertListing,
                updatedAt: Date.now()
            })
                .where(eq(airbnbListings.id, existing.id))
                .returning();
            return results[0];
        }
        else {
            // Create new listing
            const results = await db.insert(airbnbListings)
                .values({
                ...insertListing,
                createdAt: Date.now(),
                updatedAt: Date.now(),
            })
                .returning();
            return results[0];
        }
    }
    async updateAirbnbListing(id, updates) {
        const results = await db.update(airbnbListings)
            .set({
            ...updates,
            updatedAt: Date.now()
        })
            .where(eq(airbnbListings.id, id))
            .returning();
        return results[0];
    }
    // Sync Status operations
    async getSyncStatus(locationId, platform) {
        const results = await db.select().from(syncStatus)
            .where(and(eq(syncStatus.locationId, locationId), eq(syncStatus.platform, platform)));
        return results.length > 0 ? results[0] : undefined;
    }
    async saveSyncStatus(insertSyncStatus) {
        // Check if sync status already exists
        const existing = await this.getSyncStatus(insertSyncStatus.locationId, insertSyncStatus.platform);
        if (existing) {
            // Update existing sync status
            const results = await db.update(syncStatus)
                .set({
                ...insertSyncStatus,
                updatedAt: Date.now()
            })
                .where(eq(syncStatus.id, existing.id))
                .returning();
            return results[0];
        }
        else {
            // Create new sync status
            const results = await db.insert(syncStatus)
                .values({
                ...insertSyncStatus,
                createdAt: Date.now(),
                updatedAt: Date.now(),
            })
                .returning();
            return results[0];
        }
    }
    async updateSyncStatus(locationId, platform, updates) {
        const existing = await this.getSyncStatus(locationId, platform);
        if (!existing) {
            throw new Error(`Sync status not found for location ${locationId} and platform ${platform}`);
        }
        const results = await db.update(syncStatus)
            .set({
            ...updates,
            updatedAt: Date.now()
        })
            .where(eq(syncStatus.id, existing.id))
            .returning();
        return results[0];
    }
    async listSyncStatuses(hotelId) {
        if (hotelId) {
            // Join with google business locations to filter by hotel
            return db.select({
                id: syncStatus.id,
                locationId: syncStatus.locationId,
                platform: syncStatus.platform,
                lastSyncAt: syncStatus.lastSyncAt,
                lastSyncStatus: syncStatus.lastSyncStatus,
                lastSyncError: syncStatus.lastSyncError,
                reviewsCount: syncStatus.reviewsCount,
                newReviewsCount: syncStatus.newReviewsCount,
                nextSyncAt: syncStatus.nextSyncAt,
                syncIntervalMinutes: syncStatus.syncIntervalMinutes,
                isEnabled: syncStatus.isEnabled,
                createdAt: syncStatus.createdAt,
                updatedAt: syncStatus.updatedAt,
            })
                .from(syncStatus)
                .innerJoin(googleBusinessLocations, eq(syncStatus.locationId, googleBusinessLocations.id))
                .where(eq(googleBusinessLocations.hotelId, hotelId));
        }
        else {
            return db.select().from(syncStatus);
        }
    }
}
export const storage = new DatabaseStorage();
//# sourceMappingURL=storage.js.map