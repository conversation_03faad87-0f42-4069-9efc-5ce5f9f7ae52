import { describe, it, expect, beforeEach } from '@jest/globals';
import { storage } from '../storage';

describe('Storage Service', () => {
  beforeEach(async () => {
    // Clean database before each test
    // The setup.ts file handles database cleanup
  });

  describe('User Operations', () => {
    it('should create and retrieve a user', async () => {
      const userData = {
        username: 'testuser',
        password: 'hashedpassword',
        fullName: 'Test User',
      };

      const user = await storage.createUser(userData);
      expect(user).toBeDefined();
      expect(user.username).toBe(userData.username);
      expect(user.fullName).toBe(userData.fullName);

      const retrievedUser = await storage.getUser(user.id);
      expect(retrievedUser).toBeDefined();
      expect(retrievedUser?.username).toBe(userData.username);
    });

    it('should find user by username', async () => {
      const userData = {
        username: 'testuser2',
        password: 'hashedpassword',
        fullName: 'Test User 2',
      };

      await storage.createUser(userData);
      const user = await storage.getUserByUsername(userData.username);
      
      expect(user).toBeDefined();
      expect(user?.username).toBe(userData.username);
    });
  });

  describe('Hotel Operations', () => {
    it('should create and retrieve a hotel', async () => {
      const hotelData = {
        name: 'Test Hotel',
        address: '123 Test Street',
      };

      const hotel = await storage.createHotel(hotelData);
      expect(hotel).toBeDefined();
      expect(hotel.name).toBe(hotelData.name);
      expect(hotel.address).toBe(hotelData.address);

      const retrievedHotel = await storage.getHotel(hotel.id);
      expect(retrievedHotel).toBeDefined();
      expect(retrievedHotel?.name).toBe(hotelData.name);
    });
  });

  describe('Review Operations', () => {
    let hotelId: number;

    beforeEach(async () => {
      const hotel = await storage.createHotel({
        name: 'Test Hotel',
        address: '123 Test Street',
      });
      hotelId = hotel.id;
    });

    it('should create and retrieve a review', async () => {
      const reviewData = {
        hotelId,
        platform: 'google',
        externalId: 'test-review-1',
        authorName: 'John Doe',
        rating: 5,
        content: 'Great hotel!',
        date: Date.now(),
      };

      const review = await storage.saveReview(reviewData);
      expect(review).toBeDefined();
      expect(review.authorName).toBe(reviewData.authorName);
      expect(review.rating).toBe(reviewData.rating);

      const retrievedReview = await storage.getReview(review.id);
      expect(retrievedReview).toBeDefined();
      expect(retrievedReview?.content).toBe(reviewData.content);
    });

    it('should find review by external ID', async () => {
      const reviewData = {
        hotelId,
        platform: 'google',
        externalId: 'test-review-2',
        authorName: 'Jane Doe',
        rating: 4,
        content: 'Good service!',
        date: Date.now(),
      };

      await storage.saveReview(reviewData);
      const review = await storage.getReviewByExternalId('google', 'test-review-2');
      
      expect(review).toBeDefined();
      expect(review?.externalId).toBe(reviewData.externalId);
      expect(review?.platform).toBe(reviewData.platform);
    });

    it('should list reviews with filters', async () => {
      // Create multiple reviews
      const reviews = [
        {
          hotelId,
          platform: 'google',
          externalId: 'review-1',
          authorName: 'User 1',
          rating: 5,
          content: 'Excellent!',
          date: Date.now(),
        },
        {
          hotelId,
          platform: 'booking',
          externalId: 'review-2',
          authorName: 'User 2',
          rating: 3,
          content: 'Average',
          date: Date.now(),
        },
      ];

      for (const review of reviews) {
        await storage.saveReview(review);
      }

      // Test listing all reviews
      const { reviews: allReviews, total } = await storage.listReviews(hotelId);
      expect(allReviews).toHaveLength(2);
      expect(total).toBe(2);

      // Test filtering by platform
      const { reviews: googleReviews } = await storage.listReviews(hotelId, { platform: 'google' });
      expect(googleReviews).toHaveLength(1);
      expect(googleReviews[0].platform).toBe('google');
    });
  });

  describe('Platform Token Operations', () => {
    let hotelId: number;

    beforeEach(async () => {
      const hotel = await storage.createHotel({
        name: 'Test Hotel',
        address: '123 Test Street',
      });
      hotelId = hotel.id;
    });

    it('should save and retrieve platform token', async () => {
      const tokenData = {
        hotelId,
        platform: 'google',
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
        expiresAt: Date.now() + 3600000, // 1 hour from now
      };

      const token = await storage.savePlatformToken(tokenData);
      expect(token).toBeDefined();
      expect(token.platform).toBe(tokenData.platform);
      expect(token.accessToken).toBe(tokenData.accessToken);

      const retrievedToken = await storage.getPlatformToken(hotelId, 'google');
      expect(retrievedToken).toBeDefined();
      expect(retrievedToken?.accessToken).toBe(tokenData.accessToken);
    });

    it('should update existing platform token', async () => {
      const tokenData = {
        hotelId,
        platform: 'google',
        accessToken: 'old-token',
        refreshToken: 'old-refresh-token',
        expiresAt: Date.now() + 3600000,
      };

      await storage.savePlatformToken(tokenData);

      // Update with new token
      const updatedTokenData = {
        ...tokenData,
        accessToken: 'new-token',
        refreshToken: 'new-refresh-token',
      };

      const updatedToken = await storage.savePlatformToken(updatedTokenData);
      expect(updatedToken.accessToken).toBe('new-token');

      const retrievedToken = await storage.getPlatformToken(hotelId, 'google');
      expect(retrievedToken?.accessToken).toBe('new-token');
    });
  });

  describe('Google Business Location Operations', () => {
    let hotelId: number;

    beforeEach(async () => {
      const hotel = await storage.createHotel({
        name: 'Test Hotel',
        address: '123 Test Street',
      });
      hotelId = hotel.id;
    });

    it('should save and retrieve Google Business location', async () => {
      const locationData = {
        hotelId,
        googleLocationName: 'accounts/123/locations/456',
        googleLocationId: 'location-456',
        businessName: 'Test Hotel Business',
        address: '123 Business Street',
        phone: '+**********',
        isActive: 1,
      };

      const location = await storage.saveGoogleBusinessLocation(locationData);
      expect(location).toBeDefined();
      expect(location.businessName).toBe(locationData.businessName);
      expect(location.googleLocationId).toBe(locationData.googleLocationId);

      const retrievedLocation = await storage.getGoogleBusinessLocation(location.id);
      expect(retrievedLocation).toBeDefined();
      expect(retrievedLocation?.businessName).toBe(locationData.businessName);
    });

    it('should find location by Google ID', async () => {
      const locationData = {
        hotelId,
        googleLocationName: 'accounts/123/locations/789',
        googleLocationId: 'location-789',
        businessName: 'Another Test Hotel',
        address: '456 Business Avenue',
        isActive: 1,
      };

      await storage.saveGoogleBusinessLocation(locationData);
      const location = await storage.getGoogleBusinessLocationByGoogleId('location-789');
      
      expect(location).toBeDefined();
      expect(location?.googleLocationId).toBe(locationData.googleLocationId);
      expect(location?.businessName).toBe(locationData.businessName);
    });
  });

  describe('TripAdvisor Location Operations', () => {
    let hotelId: number;

    beforeEach(async () => {
      const hotel = await storage.createHotel({
        name: 'Test Hotel',
        address: '123 Test Street',
      });
      hotelId = hotel.id;
    });

    it('should save and retrieve TripAdvisor location', async () => {
      const locationData = {
        hotelId,
        tripAdvisorLocationId: 'ta-location-123',
        businessName: 'Test Hotel TripAdvisor',
        address: '123 TripAdvisor Street',
        phone: '+**********',
        website: 'https://testhotel.com',
        isActive: 1,
      };

      const location = await storage.saveTripAdvisorLocation(locationData);
      expect(location).toBeDefined();
      expect(location.businessName).toBe(locationData.businessName);
      expect(location.tripAdvisorLocationId).toBe(locationData.tripAdvisorLocationId);

      const retrievedLocation = await storage.getTripAdvisorLocation(location.id);
      expect(retrievedLocation).toBeDefined();
      expect(retrievedLocation?.businessName).toBe(locationData.businessName);
    });
  });

  describe('Booking.com Property Operations', () => {
    let hotelId: number;

    beforeEach(async () => {
      const hotel = await storage.createHotel({
        name: 'Test Hotel',
        address: '123 Test Street',
      });
      hotelId = hotel.id;
    });

    it('should save and retrieve Booking.com property', async () => {
      const propertyData = {
        hotelId,
        bookingHotelId: 'booking-hotel-123',
        propertyName: 'Test Hotel Booking',
        address: '123 Booking Street',
        city: 'Test City',
        country: 'Test Country',
        isActive: 1,
      };

      const property = await storage.saveBookingProperty(propertyData);
      expect(property).toBeDefined();
      expect(property.propertyName).toBe(propertyData.propertyName);
      expect(property.bookingHotelId).toBe(propertyData.bookingHotelId);

      const retrievedProperty = await storage.getBookingProperty(property.id);
      expect(retrievedProperty).toBeDefined();
      expect(retrievedProperty?.propertyName).toBe(propertyData.propertyName);
    });
  });

  describe('Airbnb Listing Operations', () => {
    let hotelId: number;

    beforeEach(async () => {
      const hotel = await storage.createHotel({
        name: 'Test Hotel',
        address: '123 Test Street',
      });
      hotelId = hotel.id;
    });

    it('should save and retrieve Airbnb listing', async () => {
      const listingData = {
        hotelId,
        airbnbListingId: 'airbnb-listing-123',
        listingName: 'Test Hotel Airbnb',
        propertyType: 'Apartment',
        roomType: 'Entire home/apt',
        address: '123 Airbnb Street',
        city: 'Test City',
        hostId: 'host-123',
        hostName: 'Test Host',
        isActive: 1,
      };

      const listing = await storage.saveAirbnbListing(listingData);
      expect(listing).toBeDefined();
      expect(listing.listingName).toBe(listingData.listingName);
      expect(listing.airbnbListingId).toBe(listingData.airbnbListingId);

      const retrievedListing = await storage.getAirbnbListing(listing.id);
      expect(retrievedListing).toBeDefined();
      expect(retrievedListing?.listingName).toBe(listingData.listingName);
    });
  });

  describe('Multi-Platform Review Operations', () => {
    let hotelId: number;

    beforeEach(async () => {
      const hotel = await storage.createHotel({
        name: 'Test Hotel',
        address: '123 Test Street',
      });
      hotelId = hotel.id;
    });

    it('should handle reviews from all platforms', async () => {
      const reviews = [
        {
          hotelId,
          platform: 'google',
          externalId: 'google-review-1',
          authorName: 'Google User',
          rating: 5,
          content: 'Great Google review!',
          date: Date.now(),
        },
        {
          hotelId,
          platform: 'tripadvisor',
          externalId: 'ta-review-1',
          authorName: 'TripAdvisor User',
          rating: 4,
          content: 'Good TripAdvisor review!',
          date: Date.now(),
          tripAdvisorLocationId: 'ta-location-123',
          tripType: 'Business',
        },
        {
          hotelId,
          platform: 'booking',
          externalId: 'booking-review-1',
          authorName: 'Booking User',
          rating: 4,
          content: 'Nice Booking.com review!',
          date: Date.now(),
          bookingHotelId: 'booking-hotel-123',
          reviewerCountry: 'US',
        },
        {
          hotelId,
          platform: 'airbnb',
          externalId: 'airbnb-review-1',
          authorName: 'Airbnb User',
          rating: 5,
          content: 'Amazing Airbnb review!',
          date: Date.now(),
          airbnbListingId: 'airbnb-listing-123',
          language: 'en',
        },
      ];

      // Save all reviews
      for (const review of reviews) {
        await storage.saveReview(review);
      }

      // Test listing all reviews
      const { reviews: allReviews, total } = await storage.listReviews(hotelId);
      expect(allReviews).toHaveLength(4);
      expect(total).toBe(4);

      // Test filtering by platform
      const { reviews: googleReviews } = await storage.listReviews(hotelId, { platform: 'google' });
      expect(googleReviews).toHaveLength(1);
      expect(googleReviews[0].platform).toBe('google');

      const { reviews: tripAdvisorReviews } = await storage.listReviews(hotelId, { platform: 'tripadvisor' });
      expect(tripAdvisorReviews).toHaveLength(1);
      expect(tripAdvisorReviews[0].platform).toBe('tripadvisor');

      const { reviews: bookingReviews } = await storage.listReviews(hotelId, { platform: 'booking' });
      expect(bookingReviews).toHaveLength(1);
      expect(bookingReviews[0].platform).toBe('booking');

      const { reviews: airbnbReviews } = await storage.listReviews(hotelId, { platform: 'airbnb' });
      expect(airbnbReviews).toHaveLength(1);
      expect(airbnbReviews[0].platform).toBe('airbnb');
    });
  });
});
