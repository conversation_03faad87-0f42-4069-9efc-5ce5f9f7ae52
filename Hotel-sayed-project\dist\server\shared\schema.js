import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
// User schema for authentication
export const users = sqliteTable("users", {
    id: integer("id").primaryKey({ autoIncrement: true }),
    username: text("username").notNull().unique(),
    password: text("password").notNull(),
    fullName: text("full_name").notNull(),
    role: text("role").default("staff"),
    hotelId: integer("hotel_id"),
});
export const insertUserSchema = createInsertSchema(users).pick({
    username: true,
    password: true,
    fullName: true,
});
// Hotels schema for managing multiple hotels
export const hotels = sqliteTable("hotels", {
    id: integer("id").primaryKey({ autoIncrement: true }),
    name: text("name").notNull(),
    address: text("address").notNull(),
});
export const insertHotelSchema = createInsertSchema(hotels).pick({
    name: true,
    address: true,
});
// OAuth tokens for platform connections
export const platformTokens = sqliteTable("platform_tokens", {
    id: integer("id").primaryKey({ autoIncrement: true }),
    hotelId: integer("hotel_id").notNull(),
    platform: text("platform").notNull(),
    accessToken: text("access_token").notNull(),
    refreshToken: text("refresh_token"),
    expiresAt: integer("expires_at"), // SQLite doesn't have timestamp, use integer for unix timestamp
    // Google-specific fields
    googleAccountId: text("google_account_id"), // Google Business account ID
    googleAccountName: text("google_account_name"), // Google Business account name
});
export const insertPlatformTokenSchema = createInsertSchema(platformTokens).pick({
    hotelId: true,
    platform: true,
    accessToken: true,
    refreshToken: true,
    expiresAt: true,
    googleAccountId: true,
    googleAccountName: true,
});
// Google Business Locations - maps hotel locations to Google Business locations
export const googleBusinessLocations = sqliteTable("google_business_locations", {
    id: integer("id").primaryKey({ autoIncrement: true }),
    hotelId: integer("hotel_id").notNull(),
    googleLocationName: text("google_location_name").notNull(), // Full Google location resource name
    googleLocationId: text("google_location_id").notNull(), // Google location ID
    businessName: text("business_name").notNull(),
    address: text("address"),
    phone: text("phone"),
    websiteUri: text("website_uri"),
    isActive: integer("is_active").default(1), // Whether to sync reviews from this location
    createdAt: integer("created_at").notNull().default(Date.now()),
    updatedAt: integer("updated_at").notNull().default(Date.now()),
});
export const insertGoogleBusinessLocationSchema = createInsertSchema(googleBusinessLocations).pick({
    hotelId: true,
    googleLocationName: true,
    googleLocationId: true,
    businessName: true,
    address: true,
    phone: true,
    websiteUri: true,
    isActive: true,
});
// TripAdvisor Locations - maps hotel locations to TripAdvisor locations
export const tripAdvisorLocations = sqliteTable("tripadvisor_locations", {
    id: integer("id").primaryKey({ autoIncrement: true }),
    hotelId: integer("hotel_id").notNull(),
    tripAdvisorLocationId: text("tripadvisor_location_id").notNull(),
    businessName: text("business_name").notNull(),
    address: text("address"),
    phone: text("phone"),
    website: text("website"),
    email: text("email"),
    latitude: text("latitude"),
    longitude: text("longitude"),
    numReviews: text("num_reviews"),
    rating: text("rating"),
    ranking: text("ranking"),
    isActive: integer("is_active").default(1),
    createdAt: integer("created_at").notNull().default(Date.now()),
    updatedAt: integer("updated_at").notNull().default(Date.now()),
});
export const insertTripAdvisorLocationSchema = createInsertSchema(tripAdvisorLocations).pick({
    hotelId: true,
    tripAdvisorLocationId: true,
    businessName: true,
    address: true,
    phone: true,
    website: true,
    email: true,
    latitude: true,
    longitude: true,
    numReviews: true,
    rating: true,
    ranking: true,
    isActive: true,
});
// Booking.com Properties - maps hotel locations to Booking.com properties
export const bookingProperties = sqliteTable("booking_properties", {
    id: integer("id").primaryKey({ autoIncrement: true }),
    hotelId: integer("hotel_id").notNull(),
    bookingHotelId: text("booking_hotel_id").notNull(),
    propertyName: text("property_name").notNull(),
    address: text("address"),
    city: text("city"),
    country: text("country"),
    zip: text("zip"),
    phone: text("phone"),
    email: text("email"),
    website: text("website"),
    latitude: text("latitude"),
    longitude: text("longitude"),
    starRating: integer("star_rating"),
    reviewScore: text("review_score"),
    reviewCount: integer("review_count"),
    isActive: integer("is_active").default(1),
    createdAt: integer("created_at").notNull().default(Date.now()),
    updatedAt: integer("updated_at").notNull().default(Date.now()),
});
export const insertBookingPropertySchema = createInsertSchema(bookingProperties).pick({
    hotelId: true,
    bookingHotelId: true,
    propertyName: true,
    address: true,
    city: true,
    country: true,
    zip: true,
    phone: true,
    email: true,
    website: true,
    latitude: true,
    longitude: true,
    starRating: true,
    reviewScore: true,
    reviewCount: true,
    isActive: true,
});
// Airbnb Listings - maps hotel locations to Airbnb listings
export const airbnbListings = sqliteTable("airbnb_listings", {
    id: integer("id").primaryKey({ autoIncrement: true }),
    hotelId: integer("hotel_id").notNull(),
    airbnbListingId: text("airbnb_listing_id").notNull(),
    listingName: text("listing_name").notNull(),
    propertyType: text("property_type"),
    roomType: text("room_type"),
    address: text("address"),
    city: text("city"),
    state: text("state"),
    country: text("country"),
    zipcode: text("zipcode"),
    latitude: text("latitude"),
    longitude: text("longitude"),
    accommodates: integer("accommodates"),
    bedrooms: integer("bedrooms"),
    beds: integer("beds"),
    bathrooms: integer("bathrooms"),
    price: integer("price"),
    hostId: text("host_id"),
    hostName: text("host_name"),
    isSuperhost: integer("is_superhost").default(0),
    reviewScoresRating: integer("review_scores_rating"),
    numberOfReviews: integer("number_of_reviews"),
    isActive: integer("is_active").default(1),
    createdAt: integer("created_at").notNull().default(Date.now()),
    updatedAt: integer("updated_at").notNull().default(Date.now()),
});
export const insertAirbnbListingSchema = createInsertSchema(airbnbListings).pick({
    hotelId: true,
    airbnbListingId: true,
    listingName: true,
    propertyType: true,
    roomType: true,
    address: true,
    city: true,
    state: true,
    country: true,
    zipcode: true,
    latitude: true,
    longitude: true,
    accommodates: true,
    bedrooms: true,
    beds: true,
    bathrooms: true,
    price: true,
    hostId: true,
    hostName: true,
    isSuperhost: true,
    reviewScoresRating: true,
    numberOfReviews: true,
    isActive: true,
});
// Sync Status Tracking - tracks sync operations for each location
export const syncStatus = sqliteTable("sync_status", {
    id: integer("id").primaryKey({ autoIncrement: true }),
    locationId: integer("location_id").notNull(), // References google_business_locations.id
    platform: text("platform").notNull(), // 'google', 'booking', etc.
    lastSyncAt: integer("last_sync_at"), // Last successful sync timestamp
    lastSyncStatus: text("last_sync_status").notNull().default("pending"), // 'success', 'error', 'pending', 'in_progress'
    lastSyncError: text("last_sync_error"), // Error message if sync failed
    reviewsCount: integer("reviews_count").default(0), // Number of reviews synced
    newReviewsCount: integer("new_reviews_count").default(0), // New reviews in last sync
    nextSyncAt: integer("next_sync_at"), // When next sync should happen
    syncIntervalMinutes: integer("sync_interval_minutes").default(15), // Sync frequency
    isEnabled: integer("is_enabled").default(1), // Whether sync is enabled for this location
    createdAt: integer("created_at").notNull().default(Date.now()),
    updatedAt: integer("updated_at").notNull().default(Date.now()),
});
export const insertSyncStatusSchema = createInsertSchema(syncStatus).pick({
    locationId: true,
    platform: true,
    lastSyncAt: true,
    lastSyncStatus: true,
    lastSyncError: true,
    reviewsCount: true,
    newReviewsCount: true,
    nextSyncAt: true,
    syncIntervalMinutes: true,
    isEnabled: true,
    updatedAt: true,
});
// Reviews schema
export const reviews = sqliteTable("reviews", {
    id: integer("id").primaryKey({ autoIncrement: true }),
    hotelId: integer("hotel_id").notNull(),
    platform: text("platform").notNull(),
    externalId: text("external_id").notNull(),
    authorName: text("author_name").notNull(),
    authorImage: text("author_image"),
    rating: integer("rating").notNull(),
    content: text("content").notNull(),
    date: integer("date").notNull(), // SQLite doesn't have timestamp, use integer for unix timestamp
    isReplied: integer("is_replied").default(0), // SQLite doesn't have boolean, use integer (0/1)
    // Google-specific metadata
    googleLocationId: integer("google_location_id"), // References google_business_locations.id
    googleReviewName: text("google_review_name"), // Full Google review resource name
    googleReviewUrl: text("google_review_url"), // Direct URL to the review
    updateTime: integer("update_time"), // When review was last updated on Google
    // TripAdvisor-specific metadata
    tripAdvisorLocationId: text("tripadvisor_location_id"),
    tripAdvisorUrl: text("tripadvisor_url"),
    tripType: text("trip_type"),
    travelDate: text("travel_date"),
    helpfulVotes: integer("helpful_votes").default(0),
    hasManagementResponse: integer("has_management_response").default(0),
    // Booking.com-specific metadata
    bookingHotelId: text("booking_hotel_id"),
    reviewerCountry: text("reviewer_country"),
    roomType: text("room_type"),
    groupType: text("group_type"),
    stayedNights: integer("stayed_nights"),
    isVerified: integer("is_verified").default(0),
    // Airbnb-specific metadata
    airbnbListingId: text("airbnb_listing_id"),
    reviewerId: text("reviewer_id"),
    language: text("language"),
    privateFeedback: text("private_feedback"),
    hasHostResponse: integer("has_host_response").default(0),
    // General metadata
    canReply: integer("can_reply").default(1), // Whether we can reply to this review
    replyStatus: text("reply_status").default("none"), // 'none', 'pending', 'posted', 'failed'
    lastSyncAt: integer("last_sync_at"), // When this review was last synced
    createdAt: integer("created_at").notNull().default(Date.now()),
    updatedAt: integer("updated_at").notNull().default(Date.now()),
});
export const insertReviewSchema = createInsertSchema(reviews).pick({
    hotelId: true,
    platform: true,
    externalId: true,
    authorName: true,
    authorImage: true,
    rating: true,
    content: true,
    date: true,
    // Google-specific fields
    googleLocationId: true,
    googleReviewName: true,
    googleReviewUrl: true,
    updateTime: true,
    // TripAdvisor-specific fields
    tripAdvisorLocationId: true,
    tripAdvisorUrl: true,
    tripType: true,
    travelDate: true,
    helpfulVotes: true,
    hasManagementResponse: true,
    // Booking.com-specific fields
    bookingHotelId: true,
    reviewerCountry: true,
    roomType: true,
    groupType: true,
    stayedNights: true,
    isVerified: true,
    // Airbnb-specific fields
    airbnbListingId: true,
    reviewerId: true,
    language: true,
    privateFeedback: true,
    hasHostResponse: true,
    // General fields
    canReply: true,
    replyStatus: true,
    lastSyncAt: true,
});
// Replies schema
export const replies = sqliteTable("replies", {
    id: integer("id").primaryKey({ autoIncrement: true }),
    reviewId: integer("review_id").notNull(),
    content: text("content").notNull(),
    userId: integer("user_id").notNull(),
    date: integer("date").notNull().default(Date.now()), // SQLite doesn't have timestamp, use integer for unix timestamp
    isPosted: integer("is_posted").default(0), // SQLite doesn't have boolean, use integer (0/1)
});
export const insertReplySchema = createInsertSchema(replies).pick({
    reviewId: true,
    content: true,
    userId: true,
});
// Filters and sorting types for the API
export const reviewFilterSchema = z.object({
    platform: z.enum(['all', 'google', 'booking', 'airbnb', 'tripadvisor']).optional(),
    replyStatus: z.enum(['all', 'replied', 'not-replied']).optional(),
    sortBy: z.enum(['date-desc', 'date-asc', 'rating-desc', 'rating-asc']).optional(),
    search: z.string().optional(),
    page: z.number().optional(),
    limit: z.number().optional(),
});
//# sourceMappingURL=schema.js.map