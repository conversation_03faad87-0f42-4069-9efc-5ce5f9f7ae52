import Layout from "@/components/Layout";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { FaGoogle, FaBuilding, FaAirbnb, FaPlus, FaSync, FaExternalLinkAlt, FaUnlink } from "react-icons/fa";
import PlatformConnectionModal from "@/components/PlatformConnectionModal";

// Sample platform data
const PLATFORMS = [
  {
    id: "google",
    name: "Google Business",
    description: "Manage and respond to Google Business reviews",
    icon: FaGoogle,
    iconColor: "#4285F4",
    connected: true,
    lastSync: "2 hours ago",
    reviewCount: 42
  },
  {
    id: "tripadvisor",
    name: "TripAdvisor",
    description: "Manage and respond to TripAdvisor reviews",
    icon: FaBuilding, // Using FaBuilding as placeholder for TripAdvisor
    iconColor: "#00AF87",
    connected: false,
    lastSync: "Never",
    reviewCount: 0
  },
  {
    id: "booking",
    name: "Booking.com",
    description: "Manage and respond to Booking.com reviews",
    icon: FaBuilding,
    iconColor: "#003580",
    connected: true,
    lastSync: "5 hours ago",
    reviewCount: 35
  },
  {
    id: "airbnb",
    name: "Airbnb",
    description: "Manage and respond to Airbnb reviews",
    icon: FaAirbnb,
    iconColor: "#FF5A5F",
    connected: false,
    lastSync: "Never",
    reviewCount: 0
  }
];

export default function PlatformsPage() {
  const { user, isLoading } = useAuth();
  const { toast } = useToast();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const [platforms, setPlatforms] = useState(PLATFORMS);
  
  const handleSync = (platformId: string) => {
    toast({
      title: "Syncing reviews",
      description: `Syncing reviews from ${platformId}. This may take a moment.`,
    });
    
    // Simulate syncing
    setTimeout(() => {
      toast({
        title: "Sync complete",
        description: `Successfully synced reviews from ${platformId}.`,
      });
      
      // Update the last sync time
      setPlatforms(platforms.map(p => 
        p.id === platformId ? { ...p, lastSync: "Just now" } : p
      ));
    }, 2000);
  };
  
  const handleConnect = (platformId: string) => {
    setSelectedPlatform(platformId);
    setIsModalOpen(true);
  };
  
  const handleDisconnect = (platformId: string) => {
    toast({
      title: "Confirm disconnect",
      description: `Are you sure you want to disconnect from ${platformId}?`,
      action: (
        <Button 
          variant="destructive"
          onClick={() => {
            // Simulate disconnection
            setPlatforms(platforms.map(p => 
              p.id === platformId ? { ...p, connected: false, lastSync: "Never", reviewCount: 0 } : p
            ));
            
            toast({
              title: "Platform disconnected",
              description: `Successfully disconnected from ${platformId}.`,
            });
          }}
        >
          Disconnect
        </Button>
      ),
    });
  };
  
  const handleModalClose = (success?: boolean) => {
    setIsModalOpen(false);
    
    if (success && selectedPlatform) {
      // Simulate successful connection
      setPlatforms(platforms.map(p => 
        p.id === selectedPlatform ? { ...p, connected: true, lastSync: "Just now", reviewCount: 0 } : p
      ));
      
      toast({
        title: "Platform connected",
        description: `Successfully connected to ${selectedPlatform}. Starting to sync reviews.`,
      });
    }
    
    setSelectedPlatform(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-neutral-800">Platforms</h1>
            <p className="text-neutral-500">Manage your connected review platforms</p>
          </div>
          <Button onClick={() => setIsModalOpen(true)}>
            <FaPlus className="mr-2 h-4 w-4" />
            Connect New Platform
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {platforms.map((platform) => (
            <Card key={platform.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <platform.icon className="h-6 w-6 mr-2" style={{ color: platform.iconColor }} />
                    <CardTitle>{platform.name}</CardTitle>
                  </div>
                  {platform.connected ? (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-200 border-none">
                      Connected
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-neutral-500 border-neutral-200">
                      Not Connected
                    </Badge>
                  )}
                </div>
                <CardDescription>{platform.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-neutral-500">Last Sync:</span>
                    <span className="font-medium">{platform.lastSync}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-neutral-500">Reviews:</span>
                    <span className="font-medium">{platform.reviewCount}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-4 bg-neutral-50">
                {platform.connected ? (
                  <>
                    <Button variant="outline" size="sm" onClick={() => handleSync(platform.id)}>
                      <FaSync className="mr-1 h-3 w-3" />
                      Sync
                    </Button>
                    <div className="space-x-2">
                      <Button variant="outline" size="sm">
                        <FaExternalLinkAlt className="mr-1 h-3 w-3" />
                        Visit
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDisconnect(platform.id)}>
                        <FaUnlink className="mr-1 h-3 w-3" />
                        Disconnect
                      </Button>
                    </div>
                  </>
                ) : (
                  <Button className="w-full" onClick={() => handleConnect(platform.id)}>
                    Connect
                  </Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
      
      <PlatformConnectionModal 
        isOpen={isModalOpen} 
        onClose={() => handleModalClose()} 
        platformId={selectedPlatform}
      />
    </Layout>
  );
}