import { Server as SocketIOServer } from 'socket.io';
import { syncService } from './sync-service';
export class WebSocketService {
    io;
    connectedUsers = new Map();
    constructor(server) {
        this.io = new SocketIOServer(server, {
            cors: {
                origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:5000'],
                methods: ['GET', 'POST'],
                credentials: true,
            },
        });
        this.setupEventHandlers();
        this.setupSyncServiceListeners();
    }
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`Client connected: ${socket.id}`);
            // Handle user authentication
            socket.on('authenticate', (data) => {
                this.connectedUsers.set(socket.id, {
                    userId: data.userId,
                    hotelId: data.hotelId,
                    socketId: socket.id,
                });
                socket.join(`hotel-${data.hotelId}`);
                console.log(`User ${data.userId} authenticated for hotel ${data.hotelId}`);
                // Send current sync status
                this.sendSyncStatus(socket, data.hotelId);
            });
            // Handle manual sync trigger
            socket.on('triggerSync', async (data) => {
                const user = this.connectedUsers.get(socket.id);
                if (!user) {
                    socket.emit('error', { message: 'Not authenticated' });
                    return;
                }
                try {
                    const result = await syncService.triggerSync(data.locationId, data.platform);
                    socket.emit('syncTriggered', result);
                }
                catch (error) {
                    socket.emit('error', {
                        message: 'Failed to trigger sync',
                        error: error instanceof Error ? error.message : 'Unknown error'
                    });
                }
            });
            // Handle disconnect
            socket.on('disconnect', () => {
                console.log(`Client disconnected: ${socket.id}`);
                this.connectedUsers.delete(socket.id);
            });
        });
    }
    setupSyncServiceListeners() {
        // Listen for sync completion events
        syncService.on('syncComplete', (result) => {
            this.broadcastSyncUpdate(result);
            if (result.newReviews > 0) {
                this.broadcastNewReviews(result);
            }
        });
        // Listen for sync error events
        syncService.on('syncError', (result) => {
            this.broadcastSyncError(result);
        });
    }
    async sendSyncStatus(socket, hotelId) {
        try {
            const syncStatuses = await syncService.getSyncStatuses(hotelId);
            socket.emit('syncStatus', syncStatuses);
        }
        catch (error) {
            console.error('Failed to send sync status:', error);
        }
    }
    broadcastSyncUpdate(result) {
        // Find the hotel ID for this location
        // Note: In a real implementation, you'd want to store location-to-hotel mapping
        this.io.emit('syncUpdate', {
            locationId: result.locationId,
            platform: result.platform,
            status: result.success ? 'completed' : 'failed',
            newReviews: result.newReviews,
            totalReviews: result.totalReviews,
            syncTime: result.syncTime,
            error: result.error,
        });
    }
    broadcastNewReviews(result) {
        // Broadcast notification about new reviews
        this.io.emit('newReviews', {
            locationId: result.locationId,
            platform: result.platform,
            count: result.newReviews,
            message: `${result.newReviews} new review${result.newReviews > 1 ? 's' : ''} received from ${result.platform}`,
        });
    }
    broadcastSyncError(result) {
        this.io.emit('syncError', {
            locationId: result.locationId,
            platform: result.platform,
            error: result.error,
            syncTime: result.syncTime,
        });
    }
    /**
     * Send a custom notification to users of a specific hotel
     */
    sendNotificationToHotel(hotelId, notification) {
        this.io.to(`hotel-${hotelId}`).emit('notification', notification);
    }
    /**
     * Send a custom message to a specific user
     */
    sendMessageToUser(socketId, event, data) {
        this.io.to(socketId).emit(event, data);
    }
    /**
     * Get connected users count
     */
    getConnectedUsersCount() {
        return this.connectedUsers.size;
    }
    /**
     * Get connected users for a specific hotel
     */
    getHotelUsers(hotelId) {
        return Array.from(this.connectedUsers.values()).filter(user => user.hotelId === hotelId);
    }
}
let webSocketService = null;
export function initializeWebSocket(server) {
    if (!webSocketService) {
        webSocketService = new WebSocketService(server);
    }
    return webSocketService;
}
export function getWebSocketService() {
    return webSocketService;
}
//# sourceMappingURL=websocket-service.js.map