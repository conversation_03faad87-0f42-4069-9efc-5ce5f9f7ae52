import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import Layout from "@/components/Layout";
import FilterBar from "@/components/FilterBar";
import ReviewCard from "@/components/ReviewCard";
import ExportOptions from "@/components/ExportOptions";
import { FaCommentAlt, FaStar, FaReply, FaSync, FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { Button } from "@/components/ui/button";
import { Review } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";

// Define the statistics structure
interface Statistics {
  totalReviews: number;
  pendingReplies: number;
  averageRating: string;
  platformCounts: {
    google: number;
    booking: number;
    airbnb: number;
    tripadvisor: number;
  };
}

export default function HomePage() {
  // Filter state
  const [filters, setFilters] = useState({
    platform: "all",
    replyStatus: "all",
    sortBy: "date-desc",
    search: "",
    page: 1,
    limit: 5
  });

  // Handle filter changes
  const handleFilterChange = (newFilters: any) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1 // Reset page when filters change
    }));
  };

  // Fetch reviews with filters
  const {
    data: reviewsData,
    isLoading: isLoadingReviews,
    refetch: refetchReviews
  } = useQuery<{ reviews: Review[], total: number, page: number, limit: number, totalPages: number }>({
    queryKey: ["/api/reviews", filters],
    queryFn: async () => {
      const params = new URLSearchParams();
      
      if (filters.platform) params.append("platform", filters.platform);
      if (filters.replyStatus) params.append("replyStatus", filters.replyStatus);
      if (filters.sortBy) params.append("sortBy", filters.sortBy);
      if (filters.search) params.append("search", filters.search);
      if (filters.page) params.append("page", filters.page.toString());
      if (filters.limit) params.append("limit", filters.limit.toString());
      
      const response = await fetch(`/api/reviews?${params.toString()}`, {
        credentials: "include"
      });
      
      if (!response.ok) throw new Error("Failed to fetch reviews");
      
      return response.json();
    }
  });

  // Fetch statistics
  const {
    data: statistics,
    isLoading: isLoadingStats
  } = useQuery<Statistics>({
    queryKey: ["/api/statistics"],
    queryFn: async () => {
      const response = await fetch("/api/statistics", {
        credentials: "include"
      });
      
      if (!response.ok) throw new Error("Failed to fetch statistics");
      
      return response.json();
    }
  });

  // Pagination controls
  const handleNextPage = () => {
    if (reviewsData && reviewsData.page < reviewsData.totalPages) {
      setFilters(prev => ({
        ...prev,
        page: prev.page + 1
      }));
    }
  };

  const handlePrevPage = () => {
    if (filters.page > 1) {
      setFilters(prev => ({
        ...prev,
        page: prev.page - 1
      }));
    }
  };

  const handleRefresh = () => {
    refetchReviews();
  };

  return (
    <Layout>
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-neutral-800 sm:text-3xl sm:truncate">
            Review Dashboard
          </h2>
          <div className="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
            <div className="mt-2 flex items-center text-sm text-neutral-500">
              <FaCommentAlt className="flex-shrink-0 mr-1.5 text-neutral-400" />
              {isLoadingStats ? (
                <Skeleton className="h-4 w-20" />
              ) : (
                `${statistics?.totalReviews || 0} Total Reviews`
              )}
            </div>
            <div className="mt-2 flex items-center text-sm text-neutral-500">
              <FaStar className="flex-shrink-0 mr-1.5 text-neutral-400" />
              {isLoadingStats ? (
                <Skeleton className="h-4 w-20" />
              ) : (
                `${statistics?.averageRating || "0.0"} Average Rating`
              )}
            </div>
            <div className="mt-2 flex items-center text-sm text-neutral-500">
              <FaReply className="flex-shrink-0 mr-1.5 text-neutral-400" />
              {isLoadingStats ? (
                <Skeleton className="h-4 w-20" />
              ) : (
                `${statistics?.pendingReplies || 0} Pending Replies`
              )}
            </div>
          </div>
        </div>
        <div className="flex mt-4 md:mt-0 space-x-3">
          <Button onClick={handleRefresh}>
            <FaSync className="-ml-1 mr-2 h-5 w-5" />
            Refresh Reviews
          </Button>
          <ExportOptions 
            reviews={reviewsData?.reviews || []} 
            isLoading={isLoadingReviews} 
          />
        </div>
      </div>

      {/* Filter Bar */}
      <FilterBar onFilterChange={handleFilterChange} />

      {/* Review List */}
      <div className="mt-6 space-y-4">
        {isLoadingReviews ? (
          // Loading skeletons
          Array(3).fill(0).map((_, i) => (
            <div key={i} className="bg-white shadow rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="ml-3">
                    <Skeleton className="h-4 w-36 mb-2" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Skeleton className="h-5 w-20" />
                </div>
              </div>
              <Skeleton className="h-16 w-full mt-4" />
            </div>
          ))
        ) : reviewsData?.reviews.length === 0 ? (
          <div className="bg-white shadow rounded-lg p-8 text-center">
            <h3 className="text-lg font-medium text-neutral-900 mb-2">No reviews found</h3>
            <p className="text-neutral-600">
              Try adjusting your filters or search criteria to see more results.
            </p>
          </div>
        ) : (
          reviewsData?.reviews.map((review) => (
            <ReviewCard
              key={review.id}
              id={review.id}
              platform={review.platform}
              authorName={review.authorName}
              authorImage={review.authorImage || undefined}
              rating={review.rating}
              date={typeof review.date === 'number' ? new Date(review.date).toISOString() : review.date}
              content={review.content}
              isReplied={Boolean(review.isReplied)}
              reply={review.reply ? {
                content: review.reply.content,
                date: typeof review.reply.date === 'number' ? new Date(review.reply.date).toISOString() : review.reply.date,
                userId: review.reply.userId
              } : undefined}
            />
          ))
        )}

        {/* Pagination */}
        {reviewsData && reviewsData.totalPages > 1 && (
          <div className="flex items-center justify-between px-4 py-3 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <Button 
                variant="outline" 
                onClick={handlePrevPage} 
                disabled={filters.page <= 1}
              >
                Previous
              </Button>
              <Button 
                variant="outline" 
                onClick={handleNextPage}
                disabled={reviewsData.page >= reviewsData.totalPages}
              >
                Next
              </Button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-neutral-700">
                  Showing <span className="font-medium">{(reviewsData.page - 1) * reviewsData.limit + 1}</span> to{" "}
                  <span className="font-medium">
                    {Math.min(reviewsData.page * reviewsData.limit, reviewsData.total)}
                  </span>{" "}
                  of <span className="font-medium">{reviewsData.total}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <Button 
                    variant="outline"
                    size="icon"
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-neutral-300 bg-white text-sm font-medium text-neutral-500 hover:bg-neutral-50"
                    onClick={handlePrevPage}
                    disabled={filters.page <= 1}
                  >
                    <span className="sr-only">Previous</span>
                    <FaChevronLeft className="h-5 w-5" />
                  </Button>
                  
                  {/* Page numbers */}
                  {Array.from({ length: Math.min(reviewsData.totalPages, 5) }, (_, i) => {
                    // Show pages around current page
                    let pageToShow;
                    if (reviewsData.totalPages <= 5) {
                      pageToShow = i + 1;
                    } else {
                      const start = Math.max(1, reviewsData.page - 2);
                      pageToShow = start + i;
                    }
                    
                    if (pageToShow <= reviewsData.totalPages) {
                      return (
                        <Button
                          key={pageToShow}
                          variant={pageToShow === reviewsData.page ? "default" : "outline"}
                          size="icon"
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            pageToShow === reviewsData.page
                              ? "bg-primary-50 border-primary-500 text-primary-600 z-10"
                              : "bg-white border-neutral-300 text-neutral-500 hover:bg-neutral-50"
                          }`}
                          onClick={() => setFilters(prev => ({ ...prev, page: pageToShow }))}
                        >
                          {pageToShow}
                        </Button>
                      );
                    }
                    return null;
                  })}
                  
                  <Button 
                    variant="outline"
                    size="icon"
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-neutral-300 bg-white text-sm font-medium text-neutral-500 hover:bg-neutral-50"
                    onClick={handleNextPage}
                    disabled={reviewsData.page >= reviewsData.totalPages}
                  >
                    <span className="sr-only">Next</span>
                    <FaChevronRight className="h-5 w-5" />
                  </Button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}
