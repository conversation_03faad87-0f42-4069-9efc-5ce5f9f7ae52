import { storage } from '../storage';
import { googleBusinessAPI, GoogleBusinessAPI } from './google-business-api';
import { tripAdvisorAPI, TripAdvisorAPI } from './tripadvisor-api';
import { bookingAPI, BookingAPI } from './booking-api';
import { airbnbAPI, AirbnbAPI } from './airbnb-api';
import { EventEmitter } from 'events';

export interface SyncResult {
  locationId: number;
  platform: string;
  success: boolean;
  newReviews: number;
  totalReviews: number;
  error?: string;
  syncTime: number;
}

export class SyncService extends EventEmitter {
  private syncIntervals: Map<string, NodeJS.Timeout> = new Map();
  private isRunning = false;

  constructor() {
    super();
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.on('syncComplete', (result: SyncResult) => {
      console.log(`Sync completed for location ${result.locationId}: ${result.newReviews} new reviews`);
    });

    this.on('syncError', (result: SyncResult) => {
      console.error(`Sync failed for location ${result.locationId}: ${result.error}`);
    });
  }

  /**
   * Start the sync service for all active locations
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('Sync service is already running');
      return;
    }

    this.isRunning = true;
    console.log('Starting sync service...');

    try {
      // Get all sync statuses
      const syncStatuses = await storage.listSyncStatuses();
      
      for (const syncStatus of syncStatuses) {
        if (syncStatus.isEnabled) {
          await this.scheduleLocationSync(
            syncStatus.locationId,
            syncStatus.platform,
            syncStatus.syncIntervalMinutes || 15
          );
        }
      }

      console.log(`Sync service started for ${syncStatuses.length} locations`);
    } catch (error) {
      console.error('Failed to start sync service:', error);
      this.isRunning = false;
      throw error;
    }
  }

  /**
   * Stop the sync service
   */
  stop(): void {
    console.log('Stopping sync service...');
    
    // Clear all intervals
    this.syncIntervals.forEach((interval, key) => {
      clearInterval(interval);
    });
    this.syncIntervals.clear();
    
    this.isRunning = false;
    console.log('Sync service stopped');
  }

  /**
   * Schedule sync for a specific location
   */
  private async scheduleLocationSync(locationId: number, platform: string, intervalMinutes: number): Promise<void> {
    const key = `${locationId}-${platform}`;
    
    // Clear existing interval if any
    if (this.syncIntervals.has(key)) {
      clearInterval(this.syncIntervals.get(key)!);
    }

    // Perform initial sync
    await this.syncLocation(locationId, platform);

    // Schedule recurring sync
    const interval = setInterval(async () => {
      await this.syncLocation(locationId, platform);
    }, intervalMinutes * 60 * 1000); // Convert minutes to milliseconds

    this.syncIntervals.set(key, interval);
    console.log(`Scheduled sync for location ${locationId} (${platform}) every ${intervalMinutes} minutes`);
  }

  /**
   * Sync reviews for a specific location
   */
  async syncLocation(locationId: number, platform: string): Promise<SyncResult> {
    const syncTime = Date.now();
    const result: SyncResult = {
      locationId,
      platform,
      success: false,
      newReviews: 0,
      totalReviews: 0,
      syncTime,
    };

    try {
      // Update sync status to in_progress
      await storage.updateSyncStatus(locationId, platform, {
        lastSyncStatus: 'in_progress',
        updatedAt: syncTime,
      });

      if (platform === 'google') {
        const syncResult = await this.syncGoogleLocation(locationId);
        Object.assign(result, syncResult);
      } else if (platform === 'tripadvisor') {
        const syncResult = await this.syncTripAdvisorLocation(locationId);
        Object.assign(result, syncResult);
      } else if (platform === 'booking') {
        const syncResult = await this.syncBookingLocation(locationId);
        Object.assign(result, syncResult);
      } else if (platform === 'airbnb') {
        const syncResult = await this.syncAirbnbLocation(locationId);
        Object.assign(result, syncResult);
      } else {
        throw new Error(`Platform ${platform} not supported`);
      }

      // Update sync status to success
      await storage.updateSyncStatus(locationId, platform, {
        lastSyncAt: syncTime,
        lastSyncStatus: 'success',
        lastSyncError: null,
        reviewsCount: result.totalReviews,
        newReviewsCount: result.newReviews,
        nextSyncAt: syncTime + (15 * 60 * 1000), // Next sync in 15 minutes
        updatedAt: syncTime,
      });

      result.success = true;
      this.emit('syncComplete', result);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.error = errorMessage;

      // Update sync status to error
      await storage.updateSyncStatus(locationId, platform, {
        lastSyncStatus: 'error',
        lastSyncError: errorMessage,
        updatedAt: syncTime,
      });

      this.emit('syncError', result);
    }

    return result;
  }

  /**
   * Sync Google Business location reviews
   */
  private async syncGoogleLocation(locationId: number): Promise<Partial<SyncResult>> {
    // Get Google Business location
    const location = await storage.getGoogleBusinessLocation(locationId);
    if (!location) {
      throw new Error(`Google Business location ${locationId} not found`);
    }

    // Get platform token
    const token = await storage.getPlatformToken(location.hotelId, 'google');
    if (!token) {
      throw new Error(`Google platform token not found for hotel ${location.hotelId}`);
    }

    // Set up Google API credentials
    googleBusinessAPI.setCredentials({
      access_token: token.accessToken,
      refresh_token: token.refreshToken || undefined,
      expiry_date: token.expiresAt || undefined,
    });

    // Fetch reviews from Google
    const { reviews: googleReviews } = await googleBusinessAPI.getReviews(location.googleLocationName);
    
    let newReviews = 0;
    
    // Process each review
    for (const googleReview of googleReviews) {
      // Check if review already exists
      const existingReview = await storage.getReviewByExternalId('google', googleReview.reviewId);
      
      if (!existingReview) {
        // Convert and save new review
        const baseReviewData = GoogleBusinessAPI.convertToInternalReview(googleReview, location.hotelId);
        const reviewData = {
          ...baseReviewData,
          googleLocationId: locationId,
          googleReviewName: googleReview.name,
          updateTime: new Date(googleReview.updateTime).getTime(),
          lastSyncAt: Date.now(),
        };

        await storage.saveReview(reviewData);
        newReviews++;
      }
    }

    return {
      newReviews,
      totalReviews: googleReviews.length,
    };
  }

  /**
   * Sync TripAdvisor location reviews
   */
  private async syncTripAdvisorLocation(locationId: number): Promise<Partial<SyncResult>> {
    // Get TripAdvisor location
    const location = await storage.getTripAdvisorLocation(locationId);
    if (!location) {
      throw new Error(`TripAdvisor location ${locationId} not found`);
    }

    // Fetch reviews from TripAdvisor
    const { reviews: tripAdvisorReviews } = await tripAdvisorAPI.getReviews(location.tripAdvisorLocationId);

    let newReviews = 0;

    // Process each review
    for (const tripAdvisorReview of tripAdvisorReviews) {
      // Check if review already exists
      const existingReview = await storage.getReviewByExternalId('tripadvisor', tripAdvisorReview.id);

      if (!existingReview) {
        // Convert and save new review
        const baseReviewData = TripAdvisorAPI.convertToInternalReview(tripAdvisorReview, location.hotelId);
        const reviewData = {
          ...baseReviewData,
          tripAdvisorLocationId: location.tripAdvisorLocationId,
          tripAdvisorUrl: tripAdvisorReview.url,
          tripType: tripAdvisorReview.trip_type,
          travelDate: tripAdvisorReview.travel_date,
          helpfulVotes: tripAdvisorReview.helpful_votes,
          hasManagementResponse: tripAdvisorReview.management_response ? 1 : 0,
          lastSyncAt: Date.now(),
        };

        await storage.saveReview(reviewData);
        newReviews++;
      }
    }

    return {
      newReviews,
      totalReviews: tripAdvisorReviews.length,
    };
  }

  /**
   * Sync Booking.com property reviews
   */
  private async syncBookingLocation(locationId: number): Promise<Partial<SyncResult>> {
    // Get Booking.com property
    const property = await storage.getBookingProperty(locationId);
    if (!property) {
      throw new Error(`Booking.com property ${locationId} not found`);
    }

    // Fetch reviews from Booking.com
    const { reviews: bookingReviews } = await bookingAPI.getReviews(property.bookingHotelId);

    let newReviews = 0;

    // Process each review
    for (const bookingReview of bookingReviews) {
      // Check if review already exists
      const existingReview = await storage.getReviewByExternalId('booking', bookingReview.review_id);

      if (!existingReview) {
        // Convert and save new review
        const baseReviewData = BookingAPI.convertToInternalReview(bookingReview, property.hotelId);
        const reviewData = {
          ...baseReviewData,
          bookingHotelId: bookingReview.hotel_id,
          reviewerCountry: bookingReview.reviewer_country,
          roomType: bookingReview.room_type,
          groupType: bookingReview.group_type,
          stayedNights: bookingReview.stayed_nights,
          isVerified: bookingReview.is_verified ? 1 : 0,
          helpfulVotes: bookingReview.helpful_votes,
          hasManagementResponse: bookingReview.management_response ? 1 : 0,
          lastSyncAt: Date.now(),
        };

        await storage.saveReview(reviewData);
        newReviews++;
      }
    }

    return {
      newReviews,
      totalReviews: bookingReviews.length,
    };
  }

  /**
   * Sync Airbnb listing reviews
   */
  private async syncAirbnbLocation(locationId: number): Promise<Partial<SyncResult>> {
    // Get Airbnb listing
    const listing = await storage.getAirbnbListing(locationId);
    if (!listing) {
      throw new Error(`Airbnb listing ${locationId} not found`);
    }

    // Fetch reviews from Airbnb
    const { reviews: airbnbReviews } = await airbnbAPI.getReviews(listing.airbnbListingId);

    let newReviews = 0;

    // Process each review
    for (const airbnbReview of airbnbReviews) {
      // Check if review already exists
      const existingReview = await storage.getReviewByExternalId('airbnb', airbnbReview.id);

      if (!existingReview) {
        // Convert and save new review
        const baseReviewData = AirbnbAPI.convertToInternalReview(airbnbReview, listing.hotelId);
        const reviewData = {
          ...baseReviewData,
          airbnbListingId: airbnbReview.listing_id,
          reviewerId: airbnbReview.reviewer_id,
          language: airbnbReview.language,
          privateFeedback: airbnbReview.private_feedback,
          hasHostResponse: airbnbReview.response_from_host ? 1 : 0,
          lastSyncAt: Date.now(),
        };

        await storage.saveReview(reviewData);
        newReviews++;
      }
    }

    return {
      newReviews,
      totalReviews: airbnbReviews.length,
    };
  }

  /**
   * Manually trigger sync for a specific location
   */
  async triggerSync(locationId: number, platform: string): Promise<SyncResult> {
    console.log(`Manually triggering sync for location ${locationId} (${platform})`);
    return await this.syncLocation(locationId, platform);
  }

  /**
   * Add a new location to sync
   */
  async addLocationSync(locationId: number, platform: string, intervalMinutes: number = 15): Promise<void> {
    // Create or update sync status
    await storage.saveSyncStatus({
      locationId,
      platform,
      syncIntervalMinutes: intervalMinutes,
      isEnabled: 1,
      lastSyncStatus: 'pending',
    });

    // Schedule sync if service is running
    if (this.isRunning) {
      await this.scheduleLocationSync(locationId, platform, intervalMinutes);
    }
  }

  /**
   * Remove a location from sync
   */
  async removeLocationSync(locationId: number, platform: string): Promise<void> {
    const key = `${locationId}-${platform}`;
    
    // Clear interval
    if (this.syncIntervals.has(key)) {
      clearInterval(this.syncIntervals.get(key)!);
      this.syncIntervals.delete(key);
    }

    // Disable sync status
    await storage.updateSyncStatus(locationId, platform, {
      isEnabled: 0,
      updatedAt: Date.now(),
    });
  }

  /**
   * Get sync status for all locations
   */
  async getSyncStatuses(hotelId?: number): Promise<any[]> {
    return await storage.listSyncStatuses(hotelId);
  }
}

// Export singleton instance
export const syncService = new SyncService();
