import { beforeAll, afterAll, beforeEach } from '@jest/globals';
import { storage } from '../storage';
import fs from 'fs';
import path from 'path';

const TEST_DB_PATH = path.join(__dirname, '../../test-database.sqlite');

beforeAll(async () => {
  // Set up test environment
  process.env.NODE_ENV = 'test';
  process.env.SESSION_SECRET = 'test-secret';
  
  // Clean up any existing test database
  if (fs.existsSync(TEST_DB_PATH)) {
    fs.unlinkSync(TEST_DB_PATH);
  }
});

beforeEach(async () => {
  // Clean up database before each test
  if (fs.existsSync(TEST_DB_PATH)) {
    fs.unlinkSync(TEST_DB_PATH);
  }
});

afterAll(async () => {
  // Clean up test database
  if (fs.existsSync(TEST_DB_PATH)) {
    fs.unlinkSync(TEST_DB_PATH);
  }
});
