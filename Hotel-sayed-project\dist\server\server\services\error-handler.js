import { EventEmitter } from 'events';
export var ErrorSeverity;
(function (ErrorSeverity) {
    ErrorSeverity["LOW"] = "low";
    ErrorSeverity["MEDIUM"] = "medium";
    ErrorSeverity["HIGH"] = "high";
    ErrorSeverity["CRITICAL"] = "critical";
})(ErrorSeverity || (ErrorSeverity = {}));
export var ErrorCategory;
(function (ErrorCategory) {
    ErrorCategory["API_ERROR"] = "api_error";
    ErrorCategory["DATABASE_ERROR"] = "database_error";
    ErrorCategory["AUTHENTICATION_ERROR"] = "authentication_error";
    ErrorCategory["VALIDATION_ERROR"] = "validation_error";
    ErrorCategory["SYNC_ERROR"] = "sync_error";
    ErrorCategory["NETWORK_ERROR"] = "network_error";
    ErrorCategory["RATE_LIMIT_ERROR"] = "rate_limit_error";
    ErrorCategory["UNKNOWN_ERROR"] = "unknown_error";
})(ErrorCategory || (ErrorCategory = {}));
export class ErrorHandler extends EventEmitter {
    errors = new Map();
    maxStoredErrors = 1000;
    errorRetentionHours = 24;
    constructor() {
        super();
        this.setupCleanupInterval();
    }
    /**
     * Log an error with context and severity
     */
    logError(error, category, severity = ErrorSeverity.MEDIUM, context) {
        const errorId = this.generateErrorId();
        const timestamp = Date.now();
        const errorDetails = {
            id: errorId,
            timestamp,
            severity,
            category,
            platform: context?.platform,
            locationId: context?.locationId,
            hotelId: context?.hotelId,
            message: error instanceof Error ? error.message : error,
            stack: error instanceof Error ? error.stack : undefined,
            context: context?.additionalData,
            retryCount: 0,
            resolved: false,
        };
        this.errors.set(errorId, errorDetails);
        // Emit error event for real-time monitoring
        this.emit('error', errorDetails);
        // Log to console with appropriate level
        this.logToConsole(errorDetails);
        // Handle critical errors immediately
        if (severity === ErrorSeverity.CRITICAL) {
            this.handleCriticalError(errorDetails);
        }
        // Clean up old errors if we exceed the limit
        this.cleanupOldErrors();
        return errorId;
    }
    /**
     * Mark an error as resolved
     */
    resolveError(errorId) {
        const error = this.errors.get(errorId);
        if (error) {
            error.resolved = true;
            error.resolvedAt = Date.now();
            this.emit('errorResolved', error);
            return true;
        }
        return false;
    }
    /**
     * Increment retry count for an error
     */
    incrementRetryCount(errorId) {
        const error = this.errors.get(errorId);
        if (error) {
            error.retryCount = (error.retryCount || 0) + 1;
            this.emit('errorRetry', error);
        }
    }
    /**
     * Get error metrics and statistics
     */
    getMetrics() {
        const now = Date.now();
        const oneHourAgo = now - (60 * 60 * 1000);
        const recentErrors = Array.from(this.errors.values())
            .filter(error => error.timestamp >= oneHourAgo);
        const errorsByCategory = {};
        const errorsBySeverity = {};
        const errorsByPlatform = {};
        // Initialize counters
        Object.values(ErrorCategory).forEach(category => {
            errorsByCategory[category] = 0;
        });
        Object.values(ErrorSeverity).forEach(severity => {
            errorsBySeverity[severity] = 0;
        });
        // Count errors
        Array.from(this.errors.values()).forEach(error => {
            errorsByCategory[error.category]++;
            errorsBySeverity[error.severity]++;
            if (error.platform) {
                errorsByPlatform[error.platform] = (errorsByPlatform[error.platform] || 0) + 1;
            }
        });
        return {
            totalErrors: this.errors.size,
            errorsByCategory,
            errorsBySeverity,
            errorsByPlatform,
            recentErrors: recentErrors.slice(-50), // Last 50 recent errors
            errorRate: recentErrors.length, // Errors in the last hour
        };
    }
    /**
     * Get errors by criteria
     */
    getErrors(criteria) {
        let errors = Array.from(this.errors.values());
        if (criteria) {
            if (criteria.category) {
                errors = errors.filter(error => error.category === criteria.category);
            }
            if (criteria.severity) {
                errors = errors.filter(error => error.severity === criteria.severity);
            }
            if (criteria.platform) {
                errors = errors.filter(error => error.platform === criteria.platform);
            }
            if (criteria.hotelId) {
                errors = errors.filter(error => error.hotelId === criteria.hotelId);
            }
            if (criteria.resolved !== undefined) {
                errors = errors.filter(error => error.resolved === criteria.resolved);
            }
        }
        // Sort by timestamp (newest first)
        errors.sort((a, b) => b.timestamp - a.timestamp);
        if (criteria?.limit) {
            errors = errors.slice(0, criteria.limit);
        }
        return errors;
    }
    /**
     * Clear all errors (for testing or maintenance)
     */
    clearErrors() {
        this.errors.clear();
        this.emit('errorsCleared');
    }
    /**
     * Generate a unique error ID
     */
    generateErrorId() {
        return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Log error to console with appropriate formatting
     */
    logToConsole(error) {
        const timestamp = new Date(error.timestamp).toISOString();
        const prefix = `[${timestamp}] [${error.severity.toUpperCase()}] [${error.category}]`;
        if (error.platform) {
            console.log(`${prefix} [${error.platform}]`);
        }
        console.log(`${prefix} ${error.message}`);
        if (error.context) {
            console.log(`${prefix} Context:`, error.context);
        }
        if (error.stack && error.severity === ErrorSeverity.CRITICAL) {
            console.log(`${prefix} Stack:`, error.stack);
        }
    }
    /**
     * Handle critical errors with immediate attention
     */
    handleCriticalError(error) {
        console.error('🚨 CRITICAL ERROR DETECTED 🚨');
        console.error('Error ID:', error.id);
        console.error('Message:', error.message);
        console.error('Platform:', error.platform || 'Unknown');
        console.error('Context:', error.context);
        // In production, this would trigger alerts (email, Slack, etc.)
        this.emit('criticalError', error);
    }
    /**
     * Clean up old errors to prevent memory leaks
     */
    cleanupOldErrors() {
        const now = Date.now();
        const retentionTime = this.errorRetentionHours * 60 * 60 * 1000;
        const cutoffTime = now - retentionTime;
        // Remove old errors
        for (const [id, error] of this.errors.entries()) {
            if (error.timestamp < cutoffTime) {
                this.errors.delete(id);
            }
        }
        // If still too many errors, remove oldest ones
        if (this.errors.size > this.maxStoredErrors) {
            const sortedErrors = Array.from(this.errors.entries())
                .sort(([, a], [, b]) => a.timestamp - b.timestamp);
            const toRemove = this.errors.size - this.maxStoredErrors;
            for (let i = 0; i < toRemove; i++) {
                this.errors.delete(sortedErrors[i][0]);
            }
        }
    }
    /**
     * Set up periodic cleanup of old errors
     */
    setupCleanupInterval() {
        // Clean up every hour
        setInterval(() => {
            this.cleanupOldErrors();
        }, 60 * 60 * 1000);
    }
}
/**
 * Monitoring service for tracking system health and performance
 */
export class MonitoringService extends EventEmitter {
    metrics = new Map();
    healthChecks = new Map();
    constructor() {
        super();
        this.setupDefaultHealthChecks();
        this.startHealthCheckInterval();
    }
    /**
     * Record a metric value
     */
    recordMetric(name, value, tags) {
        const timestamp = Date.now();
        const metric = {
            name,
            value,
            timestamp,
            tags: tags || {},
        };
        const key = `${name}_${timestamp}`;
        this.metrics.set(key, metric);
        this.emit('metric', metric);
    }
    /**
     * Register a health check
     */
    registerHealthCheck(name, checkFunction) {
        this.healthChecks.set(name, checkFunction);
    }
    /**
     * Run all health checks
     */
    async runHealthChecks() {
        const results = {};
        for (const [name, checkFunction] of this.healthChecks.entries()) {
            try {
                results[name] = await checkFunction();
            }
            catch (error) {
                results[name] = false;
                errorHandler.logError(error instanceof Error ? error : new Error(String(error)), ErrorCategory.UNKNOWN_ERROR, ErrorSeverity.MEDIUM, { additionalData: { healthCheck: name } });
            }
        }
        return results;
    }
    /**
     * Get system health status
     */
    async getSystemHealth() {
        const checks = await this.runHealthChecks();
        const errors = errorHandler.getMetrics();
        const uptime = process.uptime();
        const failedChecks = Object.values(checks).filter(result => !result).length;
        const totalChecks = Object.keys(checks).length;
        let status;
        if (failedChecks === 0) {
            status = 'healthy';
        }
        else if (failedChecks < totalChecks / 2) {
            status = 'degraded';
        }
        else {
            status = 'unhealthy';
        }
        return {
            status,
            checks,
            errors,
            uptime,
        };
    }
    setupDefaultHealthChecks() {
        // Database health check
        this.registerHealthCheck('database', async () => {
            try {
                // Simple database connectivity check
                return true; // Placeholder - would check actual database connection
            }
            catch {
                return false;
            }
        });
        // Memory usage health check
        this.registerHealthCheck('memory', async () => {
            const memUsage = process.memoryUsage();
            const maxMemory = 1024 * 1024 * 1024; // 1GB limit
            return memUsage.heapUsed < maxMemory;
        });
        // Error rate health check
        this.registerHealthCheck('errorRate', async () => {
            const metrics = errorHandler.getMetrics();
            return metrics.errorRate < 10; // Less than 10 errors per hour
        });
    }
    startHealthCheckInterval() {
        // Run health checks every 5 minutes
        setInterval(async () => {
            const health = await this.getSystemHealth();
            this.emit('healthCheck', health);
            if (health.status === 'unhealthy') {
                errorHandler.logError('System health check failed', ErrorCategory.UNKNOWN_ERROR, ErrorSeverity.HIGH, { additionalData: { healthStatus: health } });
            }
        }, 5 * 60 * 1000);
    }
}
// Export singleton instances
export const errorHandler = new ErrorHandler();
export const monitoringService = new MonitoringService();
//# sourceMappingURL=error-handler.js.map