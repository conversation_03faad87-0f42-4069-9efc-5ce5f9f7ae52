import axios, { AxiosInstance } from 'axios';
import { storage } from '../storage';

export interface BookingProperty {
  hotel_id: string;
  name: string;
  address: string;
  city: string;
  country: string;
  zip: string;
  latitude: number;
  longitude: number;
  phone: string;
  email: string;
  url: string;
  description: string;
  facilities: string[];
  room_types: string[];
  star_rating: number;
  review_score: number;
  review_count: number;
}

export interface BookingReview {
  review_id: string;
  hotel_id: string;
  reviewer_name: string;
  reviewer_country: string;
  reviewer_avatar?: string;
  review_date: string;
  review_score: number;
  review_title: string;
  review_positive: string;
  review_negative: string;
  room_type: string;
  trip_type: string;
  group_type: string;
  stayed_nights: number;
  language: string;
  is_verified: boolean;
  helpful_votes: number;
  management_response?: {
    response_date: string;
    response_text: string;
    responder_name: string;
  };
}

export interface BookingAPIResponse<T> {
  result: T[];
  count: number;
  total: number;
  offset: number;
  limit: number;
  has_more: boolean;
}

export class BookingAPI {
  private apiClient: AxiosInstance;
  private apiKey: string;
  private apiSecret: string;
  private baseURL = 'https://distribution-xml.booking.com/json/bookings';

  constructor() {
    this.apiKey = process.env.BOOKING_API_KEY || '';
    this.apiSecret = process.env.BOOKING_API_SECRET || '';
    
    if (!this.apiKey || !this.apiSecret) {
      console.warn('Booking.com API credentials not configured. Set BOOKING_API_KEY and BOOKING_API_SECRET environment variables.');
    }

    this.apiClient = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'Hotel Review Manager/1.0',
      },
    });

    // Add request interceptor for authentication
    this.apiClient.interceptors.request.use((config) => {
      if (this.apiKey && this.apiSecret) {
        // Booking.com uses basic authentication
        const credentials = Buffer.from(`${this.apiKey}:${this.apiSecret}`).toString('base64');
        config.headers.Authorization = `Basic ${credentials}`;
      }
      return config;
    });

    // Add response interceptor for error handling
    this.apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('Booking.com API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Search for properties by name and location
   */
  async searchProperties(query: string, city?: string, country?: string): Promise<BookingProperty[]> {
    try {
      if (!this.apiKey || !this.apiSecret) {
        console.warn('Booking.com API credentials not configured, returning mock data');
        return this.getMockProperties(query);
      }

      const response = await this.apiClient.get('/hotels', {
        params: {
          name: query,
          city,
          country,
          language: 'en',
          currency: 'USD',
        },
      });

      return response.data.result || [];
    } catch (error) {
      console.error('Error searching Booking.com properties:', error);
      // Return mock data as fallback
      return this.getMockProperties(query);
    }
  }

  /**
   * Get property details by hotel ID
   */
  async getPropertyDetails(hotelId: string): Promise<BookingProperty | null> {
    try {
      if (!this.apiKey || !this.apiSecret) {
        console.warn('Booking.com API credentials not configured, returning mock data');
        return this.getMockPropertyDetails(hotelId);
      }

      const response = await this.apiClient.get(`/hotels/${hotelId}`, {
        params: {
          language: 'en',
          currency: 'USD',
        },
      });

      return response.data || null;
    } catch (error) {
      console.error('Error fetching Booking.com property details:', error);
      return this.getMockPropertyDetails(hotelId);
    }
  }

  /**
   * Get reviews for a specific property
   */
  async getReviews(
    hotelId: string,
    limit: number = 50,
    offset: number = 0,
    language: string = 'en'
  ): Promise<{
    reviews: BookingReview[];
    totalResults: number;
    hasMore: boolean;
  }> {
    try {
      if (!this.apiKey || !this.apiSecret) {
        console.warn('Booking.com API credentials not configured, returning mock data');
        return this.getMockReviews(hotelId, limit, offset);
      }

      const response = await this.apiClient.get(`/hotels/${hotelId}/reviews`, {
        params: {
          language,
          limit,
          offset,
          sort: 'date_desc',
        },
      });

      const data = response.data;
      const reviews = data.result || [];
      const totalResults = data.total || 0;
      const hasMore = data.has_more || false;

      return {
        reviews,
        totalResults,
        hasMore,
      };
    } catch (error) {
      console.error('Error fetching Booking.com reviews:', error);
      return this.getMockReviews(hotelId, limit, offset);
    }
  }

  /**
   * Reply to a review (if supported by the property)
   */
  async replyToReview(reviewId: string, replyText: string, hotelId: string): Promise<void> {
    try {
      if (!this.apiKey || !this.apiSecret) {
        console.warn('Booking.com API credentials not configured, simulating reply');
        await this.simulateReplyToReview(reviewId, replyText, hotelId);
        return;
      }

      // Note: Booking.com review replies are typically handled through their partner portal
      // This would require special partner access and may not be available via API
      console.log(`Attempting to reply to Booking.com review ${reviewId}: ${replyText}`);
      
      // For now, log the reply for manual processing
      console.log(`Review reply for hotel ${hotelId}:`);
      console.log(`Review ID: ${reviewId}`);
      console.log(`Reply: ${replyText}`);
      console.log(`Timestamp: ${new Date().toISOString()}`);
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      console.log(`Reply logged for Booking.com review ${reviewId}. Manual posting may be required.`);
      
    } catch (error) {
      console.error('Error replying to Booking.com review:', error);
      throw new Error(`Failed to reply to Booking.com review: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert Booking.com review to internal format
   */
  static convertToInternalReview(bookingReview: BookingReview, hotelId: number) {
    // Combine positive and negative comments
    let content = '';
    if (bookingReview.review_positive) {
      content += `Positive: ${bookingReview.review_positive}`;
    }
    if (bookingReview.review_negative) {
      if (content) content += '\n\n';
      content += `Negative: ${bookingReview.review_negative}`;
    }
    if (bookingReview.review_title) {
      content = `${bookingReview.review_title}\n\n${content}`;
    }

    return {
      hotelId,
      platform: 'booking',
      externalId: bookingReview.review_id,
      authorName: bookingReview.reviewer_name,
      authorImage: bookingReview.reviewer_avatar,
      rating: Math.round(bookingReview.review_score), // Convert to 1-5 scale if needed
      content: content.trim(),
      date: new Date(bookingReview.review_date).getTime(),
      // Booking.com specific fields
      bookingHotelId: bookingReview.hotel_id,
      reviewerCountry: bookingReview.reviewer_country,
      roomType: bookingReview.room_type,
      tripType: bookingReview.trip_type,
      groupType: bookingReview.group_type,
      stayedNights: bookingReview.stayed_nights,
      isVerified: bookingReview.is_verified,
      helpfulVotes: bookingReview.helpful_votes,
      hasManagementResponse: !!bookingReview.management_response,
      lastSyncAt: Date.now(),
    };
  }

  /**
   * Simulate reply to review for development/testing
   */
  private async simulateReplyToReview(reviewId: string, replyText: string, hotelId: string): Promise<void> {
    console.log(`Simulating reply to Booking.com review ${reviewId} for hotel ${hotelId}`);
    console.log(`Reply text: ${replyText}`);
    console.log(`Timestamp: ${new Date().toISOString()}`);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log(`Simulated reply completed for review ${reviewId}`);
  }

  /**
   * Mock data for development/testing when API credentials are not available
   */
  private getMockProperties(query: string): BookingProperty[] {
    return [
      {
        hotel_id: 'mock-booking-hotel-1',
        name: `${query} - Mock Booking Hotel`,
        address: '123 Booking Street',
        city: 'Mock City',
        country: 'Mock Country',
        zip: '12345',
        latitude: 40.7128,
        longitude: -74.0060,
        phone: '******-0123',
        email: '<EMAIL>',
        url: 'https://booking.com/mock-hotel',
        description: 'A beautiful mock hotel for testing purposes.',
        facilities: ['WiFi', 'Parking', 'Restaurant', 'Gym'],
        room_types: ['Standard Room', 'Deluxe Room', 'Suite'],
        star_rating: 4,
        review_score: 8.5,
        review_count: 200,
      },
    ];
  }

  private getMockPropertyDetails(hotelId: string): BookingProperty {
    return {
      hotel_id: hotelId,
      name: 'Mock Booking Hotel Details',
      address: '123 Booking Street',
      city: 'Mock City',
      country: 'Mock Country',
      zip: '12345',
      latitude: 40.7128,
      longitude: -74.0060,
      phone: '******-0123',
      email: '<EMAIL>',
      url: 'https://booking.com/mock-hotel',
      description: 'A beautiful mock hotel for testing purposes with detailed information.',
      facilities: ['WiFi', 'Parking', 'Restaurant', 'Gym', 'Pool', 'Spa'],
      room_types: ['Standard Room', 'Deluxe Room', 'Suite', 'Presidential Suite'],
      star_rating: 4,
      review_score: 8.5,
      review_count: 200,
    };
  }

  private getMockReviews(hotelId: string, limit: number, offset: number): {
    reviews: BookingReview[];
    totalResults: number;
    hasMore: boolean;
  } {
    const mockReviews: BookingReview[] = [
      {
        review_id: `mock-booking-review-${Date.now()}-1`,
        hotel_id: hotelId,
        reviewer_name: 'Sarah M.',
        reviewer_country: 'United States',
        review_date: new Date(Date.now() - 86400000).toISOString(),
        review_score: 9.0,
        review_title: 'Excellent Stay',
        review_positive: 'The hotel was clean, staff was friendly, and the location was perfect. Great breakfast and comfortable beds.',
        review_negative: 'The WiFi could be faster in some areas of the hotel.',
        room_type: 'Deluxe Room',
        trip_type: 'Leisure',
        group_type: 'Couple',
        stayed_nights: 3,
        language: 'en',
        is_verified: true,
        helpful_votes: 5,
      },
      {
        review_id: `mock-booking-review-${Date.now()}-2`,
        hotel_id: hotelId,
        reviewer_name: 'Michael K.',
        reviewer_country: 'Germany',
        review_date: new Date(Date.now() - 172800000).toISOString(),
        review_score: 8.0,
        review_title: 'Good Value',
        review_positive: 'Nice hotel with good amenities. The restaurant food was delicious.',
        review_negative: 'The room was a bit small for the price.',
        room_type: 'Standard Room',
        trip_type: 'Business',
        group_type: 'Solo',
        stayed_nights: 2,
        language: 'en',
        is_verified: true,
        helpful_votes: 2,
      },
    ];

    return {
      reviews: mockReviews,
      totalResults: 100,
      hasMore: (offset + limit) < 100,
    };
  }
}

// Export singleton instance
export const bookingAPI = new BookingAPI();
