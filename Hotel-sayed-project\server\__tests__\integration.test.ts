import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { storage } from '../storage';
import { syncService } from '../services/sync-service';
import { googleBusinessAPI } from '../services/google-business-api';
import { tripAdvisorAPI } from '../services/tripadvisor-api';
import { bookingAPI } from '../services/booking-api';
import { airbnbAPI } from '../services/airbnb-api';
import { errorHandler, monitoringService } from '../services/error-handler';

describe('Integration Tests', () => {
  let hotelId: number;

  beforeEach(async () => {
    // Create a test hotel
    const hotel = await storage.createHotel({
      name: 'Integration Test Hotel',
      address: '123 Integration Street',
    });
    hotelId = hotel.id;

    // Clear any existing errors
    errorHandler.clearErrors();
  });

  afterEach(async () => {
    // Clean up test data
    // Note: In a real test environment, you'd want to clean up the database
  });

  describe('Multi-Platform Review Sync', () => {
    it('should sync reviews from all platforms', async () => {
      // Create platform locations for the hotel
      const googleLocation = await storage.saveGoogleBusinessLocation({
        hotelId,
        googleLocationName: 'accounts/123/locations/456',
        googleLocationId: 'location-456',
        businessName: 'Test Hotel Google',
        address: '123 Google Street',
        isActive: 1,
      });

      const tripAdvisorLocation = await storage.saveTripAdvisorLocation({
        hotelId,
        tripAdvisorLocationId: 'ta-location-789',
        businessName: 'Test Hotel TripAdvisor',
        address: '123 TripAdvisor Street',
        isActive: 1,
      });

      const bookingProperty = await storage.saveBookingProperty({
        hotelId,
        bookingHotelId: 'booking-hotel-101',
        propertyName: 'Test Hotel Booking',
        address: '123 Booking Street',
        city: 'Test City',
        country: 'Test Country',
        isActive: 1,
      });

      const airbnbListing = await storage.saveAirbnbListing({
        hotelId,
        airbnbListingId: 'airbnb-listing-202',
        listingName: 'Test Hotel Airbnb',
        propertyType: 'Hotel',
        roomType: 'Private room',
        address: '123 Airbnb Street',
        city: 'Test City',
        hostId: 'host-123',
        hostName: 'Test Host',
        isActive: 1,
      });

      // Sync all platforms
      const platforms = ['google', 'tripadvisor', 'booking', 'airbnb'];
      const locationIds = [
        googleLocation.id,
        tripAdvisorLocation.id,
        bookingProperty.id,
        airbnbListing.id,
      ];

      const syncResults = [];
      for (let i = 0; i < platforms.length; i++) {
        const result = await syncService.syncLocation(locationIds[i], platforms[i]);
        syncResults.push(result);
      }

      // Verify sync results
      expect(syncResults).toHaveLength(4);
      syncResults.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.newReviews).toBeGreaterThanOrEqual(0);
      });

      // Verify reviews were saved
      const { reviews, total } = await storage.listReviews(hotelId);
      expect(total).toBeGreaterThan(0);

      // Verify we have reviews from all platforms
      const platformsWithReviews = new Set(reviews.map(r => r.platform));
      expect(platformsWithReviews.size).toBeGreaterThan(0);
    });

    it('should handle sync errors gracefully', async () => {
      // Try to sync a non-existent location
      const result = await syncService.syncLocation(99999, 'google');
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();

      // Check that error was logged
      const errors = errorHandler.getErrors({ limit: 10 });
      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('API Service Integration', () => {
    it('should handle API calls with fallback data', async () => {
      // Test Google Business API
      const googleResult = await googleBusinessAPI.getReviews('test-location');
      expect(googleResult).toBeDefined();
      expect(googleResult.reviews).toBeDefined();
      expect(Array.isArray(googleResult.reviews)).toBe(true);

      // Test TripAdvisor API
      const tripAdvisorResult = await tripAdvisorAPI.getReviews('test-location');
      expect(tripAdvisorResult).toBeDefined();
      expect(tripAdvisorResult.reviews).toBeDefined();
      expect(Array.isArray(tripAdvisorResult.reviews)).toBe(true);

      // Test Booking.com API
      const bookingResult = await bookingAPI.getReviews('test-hotel');
      expect(bookingResult).toBeDefined();
      expect(bookingResult.reviews).toBeDefined();
      expect(Array.isArray(bookingResult.reviews)).toBe(true);

      // Test Airbnb API
      const airbnbResult = await airbnbAPI.getReviews('test-listing');
      expect(airbnbResult).toBeDefined();
      expect(airbnbResult.reviews).toBeDefined();
      expect(Array.isArray(airbnbResult.reviews)).toBe(true);
    });

    it('should convert platform reviews to internal format correctly', async () => {
      // Test Google review conversion
      const googleReview = {
        name: 'accounts/123/locations/456/reviews/789',
        reviewId: 'review-789',
        reviewer: {
          displayName: 'Test User',
          profilePhotoUrl: 'https://example.com/photo.jpg',
        },
        starRating: 'FIVE' as const,
        comment: 'Great service!',
        createTime: '2024-01-01T12:00:00Z',
        updateTime: '2024-01-01T12:00:00Z',
      };

      const internalGoogleReview = googleBusinessAPI.constructor.convertToInternalReview(googleReview, hotelId);
      expect(internalGoogleReview.platform).toBe('google');
      expect(internalGoogleReview.hotelId).toBe(hotelId);
      expect(internalGoogleReview.rating).toBe(5);

      // Test TripAdvisor review conversion
      const tripAdvisorReview = {
        id: 'ta-review-123',
        lang: 'en',
        location_id: 'ta-location-456',
        published_date: '2024-01-01T12:00:00Z',
        rating: 4,
        helpful_votes: 2,
        rating_image_url: 'https://example.com/rating.svg',
        url: 'https://tripadvisor.com/review',
        trip_type: 'Leisure',
        travel_date: '2023-12-15T00:00:00Z',
        text: 'Good experience!',
        title: 'Nice Stay',
        user: {
          user_id: 'user-123',
          member_id: 'member-123',
          type: 'user',
          first_name: 'Jane',
          last_name: 'Doe',
          avatar: {
            thumbnail: 'https://example.com/thumb.jpg',
            small: 'https://example.com/small.jpg',
            medium: 'https://example.com/medium.jpg',
            large: 'https://example.com/large.jpg',
            original: 'https://example.com/original.jpg',
          },
        },
        is_machine_translated: false,
      };

      const internalTripAdvisorReview = tripAdvisorAPI.constructor.convertToInternalReview(tripAdvisorReview, hotelId);
      expect(internalTripAdvisorReview.platform).toBe('tripadvisor');
      expect(internalTripAdvisorReview.hotelId).toBe(hotelId);
      expect(internalTripAdvisorReview.rating).toBe(4);
    });
  });

  describe('Error Handling and Monitoring', () => {
    it('should track and report errors correctly', async () => {
      // Generate some test errors
      errorHandler.logError(
        new Error('Test API error'),
        'api_error' as any,
        'medium' as any,
        { platform: 'google', hotelId }
      );

      errorHandler.logError(
        'Test validation error',
        'validation_error' as any,
        'low' as any,
        { platform: 'booking', hotelId }
      );

      // Get error metrics
      const metrics = errorHandler.getMetrics();
      expect(metrics.totalErrors).toBeGreaterThanOrEqual(2);
      expect(metrics.errorsByCategory.api_error).toBeGreaterThanOrEqual(1);
      expect(metrics.errorsByCategory.validation_error).toBeGreaterThanOrEqual(1);
      expect(metrics.errorsByPlatform.google).toBeGreaterThanOrEqual(1);
      expect(metrics.errorsByPlatform.booking).toBeGreaterThanOrEqual(1);
    });

    it('should provide system health status', async () => {
      const health = await monitoringService.getSystemHealth();
      
      expect(health).toBeDefined();
      expect(health.status).toMatch(/^(healthy|degraded|unhealthy)$/);
      expect(health.checks).toBeDefined();
      expect(health.errors).toBeDefined();
      expect(health.uptime).toBeGreaterThan(0);
    });
  });

  describe('Database Operations', () => {
    it('should handle all platform location types', async () => {
      // Test Google Business Location
      const googleLocation = await storage.saveGoogleBusinessLocation({
        hotelId,
        googleLocationName: 'accounts/123/locations/456',
        googleLocationId: 'location-456',
        businessName: 'Test Hotel Google',
        address: '123 Google Street',
        isActive: 1,
      });
      expect(googleLocation).toBeDefined();

      // Test TripAdvisor Location
      const tripAdvisorLocation = await storage.saveTripAdvisorLocation({
        hotelId,
        tripAdvisorLocationId: 'ta-location-789',
        businessName: 'Test Hotel TripAdvisor',
        address: '123 TripAdvisor Street',
        isActive: 1,
      });
      expect(tripAdvisorLocation).toBeDefined();

      // Test Booking.com Property
      const bookingProperty = await storage.saveBookingProperty({
        hotelId,
        bookingHotelId: 'booking-hotel-101',
        propertyName: 'Test Hotel Booking',
        address: '123 Booking Street',
        city: 'Test City',
        country: 'Test Country',
        isActive: 1,
      });
      expect(bookingProperty).toBeDefined();

      // Test Airbnb Listing
      const airbnbListing = await storage.saveAirbnbListing({
        hotelId,
        airbnbListingId: 'airbnb-listing-202',
        listingName: 'Test Hotel Airbnb',
        propertyType: 'Hotel',
        roomType: 'Private room',
        address: '123 Airbnb Street',
        city: 'Test City',
        hostId: 'host-123',
        hostName: 'Test Host',
        isActive: 1,
      });
      expect(airbnbListing).toBeDefined();

      // Verify all locations can be retrieved
      const googleLocations = await storage.listGoogleBusinessLocations(hotelId);
      const tripAdvisorLocations = await storage.listTripAdvisorLocations(hotelId);
      const bookingProperties = await storage.listBookingProperties(hotelId);
      const airbnbListings = await storage.listAirbnbListings(hotelId);

      expect(googleLocations).toHaveLength(1);
      expect(tripAdvisorLocations).toHaveLength(1);
      expect(bookingProperties).toHaveLength(1);
      expect(airbnbListings).toHaveLength(1);
    });
  });
});
