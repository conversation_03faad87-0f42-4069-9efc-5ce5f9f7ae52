import axios from 'axios';
export class AirbnbAPI {
    apiClient;
    accessToken;
    baseURL = 'https://api.airbnb.com/v2';
    constructor() {
        this.accessToken = process.env.AIRBNB_ACCESS_TOKEN || '';
        if (!this.accessToken) {
            console.warn('Airbnb API access token not configured. Set AIRBNB_ACCESS_TOKEN environment variable.');
        }
        this.apiClient = axios.create({
            baseURL: this.baseURL,
            timeout: 30000,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Airbnb-API-Key': process.env.AIRBNB_API_KEY || '',
            },
        });
        // Add request interceptor for authentication
        this.apiClient.interceptors.request.use((config) => {
            if (this.accessToken) {
                config.headers.Authorization = `Bearer ${this.accessToken}`;
            }
            return config;
        });
        // Add response interceptor for error handling
        this.apiClient.interceptors.response.use((response) => response, (error) => {
            console.error('Airbnb API Error:', error.response?.data || error.message);
            return Promise.reject(error);
        });
    }
    /**
     * Search for listings by location and criteria
     */
    async searchListings(location, checkin, checkout, guests) {
        try {
            if (!this.accessToken) {
                console.warn('Airbnb API access token not configured, returning mock data');
                return this.getMockListings(location);
            }
            const response = await this.apiClient.get('/search_results', {
                params: {
                    location,
                    checkin,
                    checkout,
                    guests: guests || 1,
                    locale: 'en',
                    currency: 'USD',
                },
            });
            return response.data.search_results || [];
        }
        catch (error) {
            console.error('Error searching Airbnb listings:', error);
            // Return mock data as fallback
            return this.getMockListings(location);
        }
    }
    /**
     * Get listing details by listing ID
     */
    async getListingDetails(listingId) {
        try {
            if (!this.accessToken) {
                console.warn('Airbnb API access token not configured, returning mock data');
                return this.getMockListingDetails(listingId);
            }
            const response = await this.apiClient.get(`/listings/${listingId}`, {
                params: {
                    locale: 'en',
                    currency: 'USD',
                },
            });
            return response.data.listing || null;
        }
        catch (error) {
            console.error('Error fetching Airbnb listing details:', error);
            return this.getMockListingDetails(listingId);
        }
    }
    /**
     * Get reviews for a specific listing
     */
    async getReviews(listingId, limit = 50, offset = 0) {
        try {
            if (!this.accessToken) {
                console.warn('Airbnb API access token not configured, returning mock data');
                return this.getMockReviews(listingId, limit, offset);
            }
            const response = await this.apiClient.get(`/listings/${listingId}/reviews`, {
                params: {
                    limit,
                    offset,
                    locale: 'en',
                },
            });
            const data = response.data;
            const reviews = data.reviews || [];
            const totalResults = data.metadata?.total_count || 0;
            const hasMore = data.metadata?.has_next_page || false;
            return {
                reviews,
                totalResults,
                hasMore,
            };
        }
        catch (error) {
            console.error('Error fetching Airbnb reviews:', error);
            return this.getMockReviews(listingId, limit, offset);
        }
    }
    /**
     * Reply to a review (Host response)
     */
    async replyToReview(reviewId, replyText, listingId) {
        try {
            if (!this.accessToken) {
                console.warn('Airbnb API access token not configured, simulating reply');
                await this.simulateReplyToReview(reviewId, replyText, listingId);
                return;
            }
            // Note: Airbnb review replies are typically handled through their host portal
            // API access for review responses may require special permissions
            console.log(`Attempting to reply to Airbnb review ${reviewId}: ${replyText}`);
            const response = await this.apiClient.post(`/reviews/${reviewId}/host_response`, {
                response: replyText,
            });
            if (response.status === 200 || response.status === 201) {
                console.log(`Successfully replied to Airbnb review ${reviewId}`);
            }
            else {
                throw new Error(`Unexpected response status: ${response.status}`);
            }
        }
        catch (error) {
            console.error('Error replying to Airbnb review:', error);
            // Log for manual processing if API fails
            console.log(`Review reply for listing ${listingId}:`);
            console.log(`Review ID: ${reviewId}`);
            console.log(`Reply: ${replyText}`);
            console.log(`Timestamp: ${new Date().toISOString()}`);
            throw new Error(`Failed to reply to Airbnb review: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Convert Airbnb review to internal format
     */
    static convertToInternalReview(airbnbReview, hotelId) {
        return {
            hotelId,
            platform: 'airbnb',
            externalId: airbnbReview.id,
            authorName: airbnbReview.reviewer_name,
            authorImage: airbnbReview.reviewer_profile_pic,
            rating: airbnbReview.rating || 5, // Airbnb doesn't always provide numeric ratings
            content: airbnbReview.comments,
            date: new Date(airbnbReview.date).getTime(),
            // Airbnb specific fields
            airbnbListingId: airbnbReview.listing_id,
            reviewerId: airbnbReview.reviewer_id,
            language: airbnbReview.language,
            privateFeedback: airbnbReview.private_feedback,
            hasHostResponse: !!airbnbReview.response_from_host,
            lastSyncAt: Date.now(),
        };
    }
    /**
     * Simulate reply to review for development/testing
     */
    async simulateReplyToReview(reviewId, replyText, listingId) {
        console.log(`Simulating reply to Airbnb review ${reviewId} for listing ${listingId}`);
        console.log(`Reply text: ${replyText}`);
        console.log(`Timestamp: ${new Date().toISOString()}`);
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log(`Simulated reply completed for review ${reviewId}`);
    }
    /**
     * Mock data for development/testing when API access token is not available
     */
    getMockListings(location) {
        return [
            {
                id: 'mock-airbnb-listing-1',
                name: `Beautiful ${location} Apartment`,
                description: 'A lovely apartment in the heart of the city.',
                summary: 'Perfect for travelers looking for comfort and convenience.',
                space: 'Entire apartment with modern amenities.',
                access: 'Private entrance and full access to the apartment.',
                interaction: 'Available for questions and recommendations.',
                neighborhood_overview: 'Vibrant neighborhood with restaurants and shops.',
                notes: 'Please respect the space and neighbors.',
                transit: 'Close to public transportation.',
                house_rules: 'No smoking, no parties.',
                property_type: 'Apartment',
                room_type: 'Entire home/apt',
                bed_type: 'Real Bed',
                minimum_nights: 2,
                maximum_nights: 30,
                cancellation_policy: 'moderate',
                accommodates: 4,
                bedrooms: 2,
                beds: 2,
                bathrooms: 1,
                amenities: ['WiFi', 'Kitchen', 'Heating', 'Air conditioning'],
                price: 120,
                weekly_price: 700,
                monthly_price: 2500,
                security_deposit: 200,
                cleaning_fee: 50,
                extra_people: 20,
                guests_included: 2,
                images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
                host: {
                    id: 'mock-host-1',
                    name: 'John Host',
                    since: '2020-01-01',
                    location: location,
                    about: 'Experienced host who loves welcoming guests.',
                    response_time: 'within an hour',
                    response_rate: '100%',
                    acceptance_rate: '95%',
                    is_superhost: true,
                    thumbnail_url: 'https://example.com/host-thumb.jpg',
                    picture_url: 'https://example.com/host-pic.jpg',
                    neighbourhood: 'Downtown',
                    listings_count: 3,
                    total_listings_count: 3,
                    verifications: ['email', 'phone', 'reviews'],
                },
                address: {
                    street: '123 Mock Street',
                    neighbourhood: 'Downtown',
                    neighbourhood_cleansed: 'Downtown',
                    neighbourhood_group_cleansed: 'City Center',
                    city: location,
                    state: 'Mock State',
                    zipcode: '12345',
                    market: location,
                    smart_location: `${location}, Mock State`,
                    country_code: 'US',
                    country: 'United States',
                    latitude: 40.7128,
                    longitude: -74.0060,
                    is_location_exact: true,
                },
                availability: {
                    availability_30: 25,
                    availability_60: 50,
                    availability_90: 75,
                    availability_365: 300,
                },
                review_scores: {
                    review_scores_rating: 95,
                    review_scores_accuracy: 10,
                    review_scores_cleanliness: 10,
                    review_scores_checkin: 10,
                    review_scores_communication: 10,
                    review_scores_location: 9,
                    review_scores_value: 9,
                },
                reviews_per_month: 2.5,
                number_of_reviews: 45,
                number_of_reviews_ltm: 15,
                first_review: '2020-03-15',
                last_review: '2024-01-10',
            },
        ];
    }
    getMockListingDetails(listingId) {
        const mockListings = this.getMockListings('Mock City');
        return { ...mockListings[0], id: listingId };
    }
    getMockReviews(listingId, limit, offset) {
        const mockReviews = [
            {
                id: `mock-airbnb-review-${Date.now()}-1`,
                listing_id: listingId,
                reviewer_id: 'mock-reviewer-1',
                reviewer_name: 'Emma T.',
                date: new Date(Date.now() - 86400000).toISOString(),
                comments: 'Amazing place! The host was very responsive and the apartment was exactly as described. Great location and very clean.',
                language: 'en',
                rating: 5,
            },
            {
                id: `mock-airbnb-review-${Date.now()}-2`,
                listing_id: listingId,
                reviewer_id: 'mock-reviewer-2',
                reviewer_name: 'David L.',
                date: new Date(Date.now() - 172800000).toISOString(),
                comments: 'Good value for money. The place was comfortable and well-equipped. Would stay again!',
                language: 'en',
                rating: 4,
                response_from_host: {
                    date: new Date(Date.now() - 172800000 + 3600000).toISOString(),
                    response: 'Thank you for your kind words, David! We look forward to hosting you again.',
                },
            },
        ];
        return {
            reviews: mockReviews,
            totalResults: 75,
            hasMore: (offset + limit) < 75,
        };
    }
}
// Export singleton instance
export const airbnbAPI = new AirbnbAPI();
//# sourceMappingURL=airbnb-api.js.map