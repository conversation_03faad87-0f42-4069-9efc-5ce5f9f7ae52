// Test Google Business API service
import dotenv from 'dotenv';
import { createRequire } from 'module';

const require = createRequire(import.meta.url);

dotenv.config();

async function testGoogleService() {
  console.log('🔍 Testing Google Business API Service...\n');

  try {
    // Use tsx to run TypeScript directly
    const { execSync } = require('child_process');

    // Test basic Google API configuration
    console.log('Testing Google API configuration...');
    const result = execSync('npx tsx -e "import { google } from \'googleapis\'; console.log(\'Google APIs loaded successfully\');"', { encoding: 'utf8' });
    console.log('✅', result.trim());

    // Test OAuth2 client creation
    console.log('Testing OAuth2 client creation...');
    const oauth2Test = execSync(`npx tsx -e "
      import { google } from 'googleapis';
      import dotenv from 'dotenv';
      dotenv.config();

      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        process.env.GOOGLE_REDIRECT_URI
      );

      const authUrl = oauth2Client.generateAuthUrl({
        access_type: 'offline',
        scope: ['https://www.googleapis.com/auth/business.manage'],
        prompt: 'consent',
      });

      console.log('OAuth2 client created and auth URL generated successfully');
    "`, { encoding: 'utf8' });
    console.log('✅', oauth2Test.trim());

    console.log('\n🎉 Google Business API Service Test: PASSED');

  } catch (error) {
    console.error('\n❌ Google Business API Service Test: FAILED');
    console.error('Error:', error.message);
  }
}

testGoogleService();
