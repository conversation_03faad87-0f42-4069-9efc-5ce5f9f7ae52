// Simple test script to verify Google API configuration
import dotenv from 'dotenv';
import { google } from 'googleapis';

dotenv.config();

console.log('🔍 Testing Google API Configuration...\n');

// Check environment variables
console.log('📋 Environment Variables:');
console.log('GOOGLE_CLIENT_ID:', process.env.GOOGLE_CLIENT_ID ? '✅ Set' : '❌ Missing');
console.log('GOOGLE_CLIENT_SECRET:', process.env.GOOGLE_CLIENT_SECRET ? '✅ Set' : '❌ Missing');
console.log('GOOGLE_REDIRECT_URI:', process.env.GOOGLE_REDIRECT_URI || '❌ Missing');

// Test Google APIs import
try {
  console.log('\n📦 Google APIs Library: ✅ Imported successfully');

  // Test OAuth2 client creation
  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_REDIRECT_URI
  );

  console.log('🔐 OAuth2 Client: ✅ Created successfully');

  // Test auth URL generation
  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: ['https://www.googleapis.com/auth/business.manage'],
    prompt: 'consent',
  });

  console.log('🔗 Auth URL: ✅ Generated successfully');
  console.log('📝 Sample Auth URL:', authUrl.substring(0, 100) + '...');

  console.log('\n🎉 Google API Configuration Test: PASSED');
  console.log('\n📋 Next Steps:');
  console.log('1. Start your server: npm run dev');
  console.log('2. Login to your hotel dashboard');
  console.log('3. Go to Platforms page');
  console.log('4. Click "Connect with Google Business"');

} catch (error) {
  console.log('\n❌ Google APIs Library Error:', error.message);
  console.log('\n🔧 Try running: npm install googleapis');
}
