import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { GoogleBusinessAPI } from '../services/google-business-api';
import { google } from 'googleapis';

// Mock the googleapis module
jest.mock('googleapis');
jest.mock('axios');

const mockGoogle = google as jest.Mocked<typeof google>;

describe('GoogleBusinessAPI', () => {
  let googleAPI: GoogleBusinessAPI;
  let mockOAuth2Client: any;
  let mockBusinessProfileAPI: any;
  let mockPlacesAPI: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock OAuth2 client
    mockOAuth2Client = {
      generateAuthUrl: jest.fn(),
      getToken: jest.fn(),
      setCredentials: jest.fn(),
      refreshAccessToken: jest.fn(),
      credentials: {}
    };

    // Mock Business Profile API
    mockBusinessProfileAPI = {
      accounts: {
        locations: {
          list: jest.fn()
        }
      }
    };

    // Mock Places API
    mockPlacesAPI = {
      places: {
        get: jest.fn()
      }
    };

    // Mock google.auth.OAuth2 constructor
    mockGoogle.auth = {
      OAuth2: jest.fn().mockImplementation(() => mockOAuth2Client)
    } as any;

    // Mock API clients
    mockGoogle.mybusinessbusinessinformation = jest.fn().mockReturnValue(mockBusinessProfileAPI);
    mockGoogle.places = jest.fn().mockReturnValue(mockPlacesAPI);
    mockGoogle.mybusinessaccountmanagement = jest.fn().mockReturnValue({
      accounts: {
        list: jest.fn()
      }
    });

    googleAPI = new GoogleBusinessAPI();
  });

  describe('Constructor', () => {
    it('should initialize OAuth2 client with correct credentials', () => {
      expect(mockGoogle.auth.OAuth2).toHaveBeenCalledWith(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        process.env.GOOGLE_REDIRECT_URI
      );
    });

    it('should initialize Business Profile API client', () => {
      expect(mockGoogle.mybusinessbusinessinformation).toHaveBeenCalledWith({
        version: 'v1',
        auth: mockOAuth2Client
      });
    });

    it('should initialize Places API client', () => {
      expect(mockGoogle.places).toHaveBeenCalledWith({
        version: 'v1',
        auth: mockOAuth2Client
      });
    });
  });

  describe('getAuthUrl', () => {
    it('should generate auth URL with correct scopes', () => {
      const mockAuthUrl = 'https://accounts.google.com/oauth2/auth?...';
      mockOAuth2Client.generateAuthUrl.mockReturnValue(mockAuthUrl);

      const result = googleAPI.getAuthUrl();

      expect(mockOAuth2Client.generateAuthUrl).toHaveBeenCalledWith({
        access_type: 'offline',
        scope: [
          'https://www.googleapis.com/auth/business.manage',
          'https://www.googleapis.com/auth/places'
        ],
        prompt: 'consent'
      });
      expect(result).toBe(mockAuthUrl);
    });
  });

  describe('getTokensFromCode', () => {
    it('should exchange code for tokens', async () => {
      const mockTokens = {
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        expiry_date: Date.now() + 3600000
      };

      mockOAuth2Client.getToken.mockResolvedValue({ tokens: mockTokens });

      const result = await googleAPI.getTokensFromCode('test-code');

      expect(mockOAuth2Client.getToken).toHaveBeenCalledWith('test-code');
      expect(result).toEqual({
        access_token: mockTokens.access_token,
        refresh_token: mockTokens.refresh_token,
        expiry_date: mockTokens.expiry_date
      });
    });
  });

  describe('setCredentials', () => {
    it('should set credentials on OAuth2 client', () => {
      const credentials = {
        access_token: 'test-token',
        refresh_token: 'test-refresh',
        expiry_date: Date.now() + 3600000
      };

      googleAPI.setCredentials(credentials);

      expect(mockOAuth2Client.setCredentials).toHaveBeenCalledWith(credentials);
    });
  });

  describe('ensureValidCredentials', () => {
    it('should return true for valid non-expired credentials', async () => {
      mockOAuth2Client.credentials = {
        access_token: 'valid-token',
        expiry_date: Date.now() + 3600000
      };

      const result = await googleAPI.ensureValidCredentials();

      expect(result).toBe(true);
    });

    it('should return false when no access token', async () => {
      mockOAuth2Client.credentials = {};

      const result = await googleAPI.ensureValidCredentials();

      expect(result).toBe(false);
    });

    it('should refresh expired token when refresh token available', async () => {
      const expiredTime = Date.now() - 1000;
      mockOAuth2Client.credentials = {
        access_token: 'expired-token',
        refresh_token: 'refresh-token',
        expiry_date: expiredTime
      };

      const newCredentials = {
        access_token: 'new-token',
        expiry_date: Date.now() + 3600000
      };

      mockOAuth2Client.refreshAccessToken.mockResolvedValue({
        credentials: newCredentials
      });

      const result = await googleAPI.ensureValidCredentials();

      expect(mockOAuth2Client.refreshAccessToken).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('getReviews', () => {
    it('should return fallback reviews when place ID cannot be extracted', async () => {
      mockOAuth2Client.credentials = {
        access_token: 'valid-token',
        expiry_date: Date.now() + 3600000
      };

      const result = await googleAPI.getReviews('invalid-location-name');

      expect(result.reviews).toHaveLength(2);
      expect(result.reviews[0].reviewer.displayName).toBe('Sample Customer');
      expect(result.totalReviewCount).toBe(2);
    });

    it('should handle API errors gracefully', async () => {
      mockOAuth2Client.credentials = {
        access_token: 'valid-token',
        expiry_date: Date.now() + 3600000
      };

      // Mock axios to throw an error
      const axios = require('axios');
      axios.get = jest.fn().mockRejectedValue(new Error('API Error'));

      const result = await googleAPI.getReviews('locations/test-place-id');

      expect(result.reviews).toHaveLength(2); // Should return fallback reviews
      expect(result.reviews[0].reviewer.displayName).toBe('Sample Customer');
    });
  });

  describe('Static helper methods', () => {
    it('should convert star rating correctly', () => {
      expect(GoogleBusinessAPI.convertStarRating('ONE')).toBe(1);
      expect(GoogleBusinessAPI.convertStarRating('TWO')).toBe(2);
      expect(GoogleBusinessAPI.convertStarRating('THREE')).toBe(3);
      expect(GoogleBusinessAPI.convertStarRating('FOUR')).toBe(4);
      expect(GoogleBusinessAPI.convertStarRating('FIVE')).toBe(5);
      expect(GoogleBusinessAPI.convertStarRating('INVALID')).toBe(0);
    });

    it('should convert Google review to internal format', () => {
      const googleReview = {
        reviewId: 'test-review-id',
        reviewer: {
          displayName: 'Test User',
          profilePhotoUrl: 'https://example.com/photo.jpg'
        },
        starRating: 'FIVE',
        comment: 'Great hotel!',
        createTime: '2023-01-01T00:00:00Z'
      };

      const result = GoogleBusinessAPI.convertToInternalReview(googleReview, 1);

      expect(result).toEqual({
        hotelId: 1,
        platform: 'google',
        externalId: 'test-review-id',
        authorName: 'Test User',
        authorImage: 'https://example.com/photo.jpg',
        rating: 5,
        content: 'Great hotel!',
        date: new Date('2023-01-01T00:00:00Z').getTime()
      });
    });
  });
});
