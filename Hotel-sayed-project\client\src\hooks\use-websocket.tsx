import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './use-auth';

export interface SyncStatus {
  id: number;
  locationId: number;
  platform: string;
  lastSyncAt?: number | null;
  lastSyncStatus: string;
  lastSyncError?: string | null;
  reviewsCount: number;
  newReviewsCount: number;
  nextSyncAt?: number | null;
  syncIntervalMinutes: number;
  isEnabled: boolean;
  createdAt?: number;
  updatedAt?: number;
}

export interface SyncUpdate {
  locationId: number;
  platform: string;
  status: 'completed' | 'failed';
  newReviews: number;
  totalReviews: number;
  syncTime: number;
  error?: string;
}

export interface NewReviewsNotification {
  locationId: number;
  platform: string;
  count: number;
  message: string;
}

export interface WebSocketHook {
  socket: Socket | null;
  connected: boolean;
  syncStatuses: SyncStatus[];
  triggerSync: (locationId: number, platform: string) => void;
  onSyncUpdate: (callback: (update: SyncUpdate) => void) => () => void;
  onNewReviews: (callback: (notification: NewReviewsNotification) => void) => () => void;
  onSyncError: (callback: (error: any) => void) => () => void;
}

export function useWebSocket(): WebSocketHook {
  const { user } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [syncStatuses, setSyncStatuses] = useState<SyncStatus[]>([]);
  
  const syncUpdateCallbacks = useRef<((update: SyncUpdate) => void)[]>([]);
  const newReviewsCallbacks = useRef<((notification: NewReviewsNotification) => void)[]>([]);
  const syncErrorCallbacks = useRef<((error: any) => void)[]>([]);

  useEffect(() => {
    if (!user) return;

    // Initialize socket connection
    const newSocket = io(window.location.origin, {
      transports: ['websocket', 'polling'],
    });

    newSocket.on('connect', () => {
      console.log('WebSocket connected');
      setConnected(true);
      
      // Authenticate with the server
      newSocket.emit('authenticate', {
        userId: user.id,
        hotelId: user.hotelId,
      });
    });

    newSocket.on('disconnect', () => {
      console.log('WebSocket disconnected');
      setConnected(false);
    });

    newSocket.on('syncStatus', (statuses: SyncStatus[]) => {
      setSyncStatuses(statuses);
    });

    newSocket.on('syncUpdate', (update: SyncUpdate) => {
      // Update sync statuses
      setSyncStatuses(prev => prev.map(status =>
        status.locationId === update.locationId && status.platform === update.platform
          ? {
              ...status,
              lastSyncAt: update.syncTime,
              lastSyncStatus: update.status === 'completed' ? 'success' : 'error',
              lastSyncError: update.error || null,
              reviewsCount: update.totalReviews,
              newReviewsCount: update.newReviews,
            } as SyncStatus
          : status
      ));

      // Call registered callbacks
      syncUpdateCallbacks.current.forEach(callback => callback(update));
    });

    newSocket.on('newReviews', (notification: NewReviewsNotification) => {
      newReviewsCallbacks.current.forEach(callback => callback(notification));
    });

    newSocket.on('syncError', (error: any) => {
      syncErrorCallbacks.current.forEach(callback => callback(error));
    });

    newSocket.on('error', (error: any) => {
      console.error('WebSocket error:', error);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [user]);

  const triggerSync = (locationId: number, platform: string) => {
    if (socket && connected) {
      socket.emit('triggerSync', { locationId, platform });
    }
  };

  const onSyncUpdate = (callback: (update: SyncUpdate) => void) => {
    syncUpdateCallbacks.current.push(callback);
    
    // Return cleanup function
    return () => {
      const index = syncUpdateCallbacks.current.indexOf(callback);
      if (index > -1) {
        syncUpdateCallbacks.current.splice(index, 1);
      }
    };
  };

  const onNewReviews = (callback: (notification: NewReviewsNotification) => void) => {
    newReviewsCallbacks.current.push(callback);
    
    return () => {
      const index = newReviewsCallbacks.current.indexOf(callback);
      if (index > -1) {
        newReviewsCallbacks.current.splice(index, 1);
      }
    };
  };

  const onSyncError = (callback: (error: any) => void) => {
    syncErrorCallbacks.current.push(callback);
    
    return () => {
      const index = syncErrorCallbacks.current.indexOf(callback);
      if (index > -1) {
        syncErrorCallbacks.current.splice(index, 1);
      }
    };
  };

  return {
    socket,
    connected,
    syncStatuses,
    triggerSync,
    onSyncUpdate,
    onNewReviews,
    onSyncError,
  };
}
