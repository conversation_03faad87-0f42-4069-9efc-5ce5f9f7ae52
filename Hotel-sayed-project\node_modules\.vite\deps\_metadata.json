{"hash": "f2023c61", "configHash": "efece226", "lockfileHash": "b81b495f", "browserHash": "c46a2e3b", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "e73ae362", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "83cbe245", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "862ab26a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ba716dcc", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "0d82661f", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "8fc97dc9", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "7f1b3878", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "961e84b4", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "a77104af", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "41d424d1", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "85057401", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "939359b5", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "dc7b102d", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "e4a1c006", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "53edf8b0", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "5a889471", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "f1016e1a", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.mjs", "file": "date-fns.js", "fileHash": "7cb55ade", "needsInterop": false}, "jspdf": {"src": "../../jspdf/dist/jspdf.es.min.js", "file": "jspdf.js", "fileHash": "4f1873c5", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "966d0803", "needsInterop": false}, "react-day-picker": {"src": "../../react-day-picker/dist/index.esm.js", "file": "react-day-picker.js", "fileHash": "3ce9b521", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "49351d79", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "4681776b", "needsInterop": false}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "ffdab4ae", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "274750c4", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "23e08dd6", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "a9b5193d", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "32a9583a", "needsInterop": false}, "zod": {"src": "../../zod/lib/index.mjs", "file": "zod.js", "fileHash": "0a203426", "needsInterop": false}}, "chunks": {"html2canvas.esm-H3DJHMJZ": {"file": "html2canvas__esm-H3DJHMJZ.js"}, "purify.es-ZJB4YCCD": {"file": "purify__es-ZJB4YCCD.js"}, "index.es-DRYKCZ6N": {"file": "index__es-DRYKCZ6N.js"}, "chunk-D7ZASVPN": {"file": "chunk-D7ZASVPN.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-6PJ6ZD5Z": {"file": "chunk-6PJ6ZD5Z.js"}, "chunk-YABUFNLM": {"file": "chunk-YABUFNLM.js"}, "chunk-W72DBEYB": {"file": "chunk-W72DBEYB.js"}, "chunk-OKX5DAGM": {"file": "chunk-OKX5DAGM.js"}, "chunk-EZD6FE6J": {"file": "chunk-EZD6FE6J.js"}, "chunk-PQPPOKUW": {"file": "chunk-PQPPOKUW.js"}, "chunk-O2CMLVAY": {"file": "chunk-O2CMLVAY.js"}, "chunk-LPGE2D4Z": {"file": "chunk-LPGE2D4Z.js"}, "chunk-XXYU4X4D": {"file": "chunk-XXYU4X4D.js"}, "chunk-FQBNBWJQ": {"file": "chunk-FQBNBWJQ.js"}, "chunk-6J5IXUBZ": {"file": "chunk-6J5IXUBZ.js"}, "chunk-CPFL7ZFD": {"file": "chunk-CPFL7ZFD.js"}, "chunk-DKHUMOWT": {"file": "chunk-DKHUMOWT.js"}, "chunk-RPCDYKBN": {"file": "chunk-RPCDYKBN.js"}, "chunk-KBTYAULA": {"file": "chunk-KBTYAULA.js"}, "chunk-QCHXOAYK": {"file": "chunk-QCHXOAYK.js"}, "chunk-WOOG5QLI": {"file": "chunk-WOOG5QLI.js"}}}