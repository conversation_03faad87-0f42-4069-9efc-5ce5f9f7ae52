{"compilerOptions": {"noEmit": false, "outDir": "../dist/server", "rootDir": "..", "module": "ESNext", "target": "ES2022", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "declaration": false, "sourceMap": true, "types": ["node"], "baseUrl": "..", "paths": {"@shared/*": ["shared/*"]}}, "include": ["**/*.ts", "../shared/**/*.ts"], "exclude": ["**/*.test.ts", "**/__tests__/**/*", "../vite.config.ts", "vite.ts"]}